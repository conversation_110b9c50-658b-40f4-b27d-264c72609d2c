import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

const workspaceId = Deno.env.get("MESSAGEBIRD_WORKSPACE_ID");
const channelId = Deno.env.get("MESSAGEBIRD_CHANNEL_ID");
const accessKey = Deno.env.get("MESSAGEBIRD_ACCESS_KEY");

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { to, message } = await req.json();

    if (!to || !message) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    const messageBirdEndpoint = `https://api.bird.com/workspaces/${workspaceId}/channels/${channelId}/messages`;

    const result = await fetch(messageBirdEndpoint, {
      method: "POST",
      headers: {
        Authorization: `AccessKey ${accessKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        receiver: {
          contacts: [
            {
              identifierValue: to,
            },
          ],
        },
        body: {
          type: "text",
          text: {
            text: message,
          },
        },
      }),
    });

    const data = await result.json();

    if (!result.ok) {
      throw new Error(data.message || "Failed to send SMS");
    }

    return new Response(
      JSON.stringify({ success: true, messageSid: data.sid }),
      { headers: { "Content-Type": "application/json", ...corsHeaders } }
    );
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }
});
