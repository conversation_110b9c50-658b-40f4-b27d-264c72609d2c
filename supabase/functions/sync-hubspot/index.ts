import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.0'

const HUBSPOT_API_KEY = Deno.env.get('HUBSPOT_API_KEY')
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')
const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY')

const supabase = createClient(SUPABASE_URL!, SUPABASE_ANON_KEY!)

Deno.serve(async (req) => {
  try {
    // Get profiles from Supabase
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      
    if (error) throw error

    // For each profile, sync to HubSpot
    for (const profile of profiles) {
      const hubspotContact = {
        properties: {
          email: profile.email,
          firstname: profile.first_name,
          lastname: profile.last_name,
          phone: profile.phone_number,
          company: profile.company_name,
          user_type: profile.user_type,
        },
      }

      // Send to HubSpot
      const response = await fetch('https://api.hubapi.com/crm/v3/objects/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${HUBSPOT_API_KEY}`,
        },
        body: JSON.stringify(hubspotContact),
      })

      if (!response.ok) {
        console.error(`Failed to sync contact ${profile.email} to HubSpot`)
        continue
      }

      console.log(`Successfully synced contact ${profile.email} to HubSpot`)
    }

    return new Response(JSON.stringify({ message: 'Sync completed successfully' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error syncing to HubSpot:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})