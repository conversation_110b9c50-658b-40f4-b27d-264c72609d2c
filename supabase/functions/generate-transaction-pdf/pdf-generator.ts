import { PDFDocument, rgb } from 'https://esm.sh/pdf-lib@1.17.1';

export const generateTransactionPDF = async (transaction: any, profile: any) => {
  console.log('Starting PDF generation for transaction:', transaction.id);
  
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
  const { width, height } = page.getSize();

  // Add header
  page.drawText('Transaction Receipt', {
    x: 50,
    y: height - 50,
    size: 20,
    color: rgb(0, 0, 0)
  });

  // Add transaction details
  const startY = height - 100;
  const lineHeight = 20;
  let currentY = startY;

  // Transaction ID
  page.drawText(`Transaction ID: ${transaction.id}`, {
    x: 50,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0)
  });
  currentY -= lineHeight;

  // Date
  const date = new Date(transaction.created_at).toLocaleDateString('nl-NL');
  page.drawText(`Date: ${date}`, {
    x: 50,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0)
  });
  currentY -= lineHeight * 2;

  // Customer details section
  currentY = addCustomerDetails(page, currentY, profile);

  // Transaction details section
  currentY = addTransactionDetails(page, currentY, transaction);

  console.log('PDF generation completed, saving document...');
  return pdfDoc.save();
};

const addCustomerDetails = (page: any, startY: number, profile: any) => {
  let currentY = startY;

  page.drawText('Customer Details:', {
    x: 50,
    y: currentY,
    size: 14,
    color: rgb(0, 0, 0)
  });
  currentY -= 20;

  if (profile) {
    page.drawText(`Name: ${profile.first_name} ${profile.last_name}`, {
      x: 50,
      y: currentY,
      size: 12,
      color: rgb(0, 0, 0)
    });
    currentY -= 20;

    if (profile.company_name) {
      page.drawText(`Company: ${profile.company_name}`, {
        x: 50,
        y: currentY,
        size: 12,
        color: rgb(0, 0, 0)
      });
      currentY -= 20;
    }

    if (profile.btw_number) {
      page.drawText(`BTW Number: ${profile.btw_number}`, {
        x: 50,
        y: currentY,
        size: 12,
        color: rgb(0, 0, 0)
      });
      currentY -= 20;
    }

    const address = `${profile.street_address} ${profile.house_number}${profile.house_number_addition || ''}`;
    page.drawText(`Address: ${address}`, {
      x: 50,
      y: currentY,
      size: 12,
      color: rgb(0, 0, 0)
    });
    currentY -= 40;
  }

  return currentY;
};

const addTransactionDetails = (page: any, startY: number, transaction: any) => {
  let currentY = startY;

  page.drawText('Transaction Details:', {
    x: 50,
    y: currentY,
    size: 14,
    color: rgb(0, 0, 0)
  });
  currentY -= 20;

  page.drawText(`Amount: €${transaction.amount.toFixed(2)}`, {
    x: 50,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0)
  });
  currentY -= 20;

  page.drawText(`Type: ${transaction.type}`, {
    x: 50,
    y: currentY,
    size: 12,
    color: rgb(0, 0, 0)
  });
  currentY -= 20;

  if (transaction.description) {
    page.drawText(`Description: ${transaction.description}`, {
      x: 50,
      y: currentY,
      size: 12,
      color: rgb(0, 0, 0)
    });
  }

  return currentY;
};