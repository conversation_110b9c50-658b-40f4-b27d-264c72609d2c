import { z } from "zod";

export const jobSchema = z.object({
	description: z
		.string()
		.min(20, "Beschrijving moet minimaal 20 karakters bevatten")
		.max(1000, "Beschrijving mag maximaal 1000 karakters bevatten"),
	preferredDate: z.string().optional(),
	additionalNotes: z.string().optional(),
	postal_code: z
		.string()
		.regex(
			/^[1-9][0-9]{3} ?[A-Z]{2}$/i,
			"Vul een geldige postcode in (bijv. 1234 AB)"
		),
	house_number: z
		.string()
		.min(1, "Vul een huisnummer in")
		.max(10, "Huisnummer mag maximaal 10 karakters bevatten"),
	house_number_addition: z.string().optional(),
	photos: z
		.array(z.instanceof(File))
		.max(4, "Je kunt maximaal 4 foto's uploaden")
		.optional(),
});

export type JobFormData = z.infer<typeof jobSchema>;
