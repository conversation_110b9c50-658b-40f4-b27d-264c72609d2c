import { createContext, PropsWithChildren, useContext, useEffect } from "react";

import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { SessionContext } from "@/components/auth/SessionProvider";
import { useToast } from "@/hooks/use-toast";

interface JobsContextProps {
  jobs: any[];
  isLoading: boolean;
}

export const JobsContext = createContext<JobsContextProps>({
  jobs: [],
  isLoading: false,
});

export const JobsProvider = ({ children }: PropsWithChildren) => {
  const { userProfile } = useContext(SessionContext);
  const { toast } = useToast();

  const {
    data: jobs,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["admin-jobs"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("jobs")
        .select(
          `
              *,
              profiles!jobs_user_id_fkey (
                id,
                created_at,
                first_name,
                last_name,
                email,
                phone_number,
                user_type,
                company_name,
                kvk_number,
                btw_number,
                profile_photo_url
              ),
              job_responses (
                id,
                status,
                message,
                created_at,
                profiles (
                  id,
                  first_name,
                  last_name,
                  email,
                  phone_number,
                  user_type,
                  company_name,
                  kvk_number,
                  btw_number,
                  profile_photo_url
                )
              ),
              messages (
                id,
                content,
                created_at,
                sender_id,
                receiver_id,
                profiles!messages_sender_id_fkey (
                  id,
                  first_name,
                  last_name,
                  profile_photo_url
                )
              )
            `
        )
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching admin jobs and messages:", error);
        throw error;
      }

      return data;
    },
  });

  useEffect(() => {
    if (!userProfile) return;

    const handleJobCreation = async (payload: any) => {
      const { user_type } = userProfile;
      const { title } = payload.new;

      const toastMessages = {
        vakman: {
          title: "Nieuwe baan gecreëerd",
          description: title,
        },
        admin: {
          title: "Nieuwe baan is aangemaakt, accepteer deze alstublieft",
          description: title,
        },
      };

      if (toastMessages[user_type]) {
        toast(toastMessages[user_type]);
        if (user_type === "admin") {
          await refetch();
        }
      }
    };

    const channel = supabase
      .channel("schema-db-changes")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "jobs",
        },
        handleJobCreation
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [userProfile, toast, refetch]);

  return (
    <JobsContext.Provider value={{ jobs, isLoading }}>
      {children}
    </JobsContext.Provider>
  );
};

export const useJobs = () => {
  const context = useContext(JobsContext);
  if (context === undefined) {
    throw new Error("useJobs must be used within a JobsProvider.");
  }

  return context;
};
