import { format, isValid, parseISO } from "date-fns";
import { nl } from "date-fns/locale";
import { Clock, CheckCircle2 } from "lucide-react";

import { Chat } from "./types";
import { ChatAvatar } from "./ChatAvatar";
import { cn } from "@/lib/utils";

interface ChatListItemProps {
  chat: Chat;
  isActive: boolean;
  onClick: () => void;
}

export const ChatListItem = ({
  chat,
  isActive,
  onClick,
}: ChatListItemProps) => {
  // Add null checks and default values
  if (!chat || !chat.jobs || !chat.profiles) {
    console.error("Invalid chat data:", chat);
    return null;
  }

  const createdDate = chat.jobs?.created_at
    ? parseISO(chat.jobs.created_at)
    : null;
  const formattedDate =
    createdDate && isValid(createdDate)
      ? format(createdDate, "d MMM", { locale: nl })
      : "";

  const displayName =
    chat.profiles.company_name ||
    chat.profiles.full_name ||
    `${chat.profiles.first_name || ""} ${
      chat.profiles.last_name || ""
    }`.trim() ||
    "Onbekende gebruiker";

  return (
    <div
      className={cn(
        "rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md border border-border/30 backdrop-blur-sm",
        isActive
          ? "bg-primary text-white shadow-lg shadow-primary/20"
          : "bg-card hover:bg-secondary/50"
      )}
      onClick={onClick}
    >
      <div className="p-4">
        <div className="flex items-center gap-3">
          <ChatAvatar profilePhotoUrl={chat.profiles.profile_photo_url} />
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start gap-2">
              <div>
                <h3 className="font-medium truncate text-base">
                  {chat.jobs.title || "Geen titel"}
                </h3>
                <p
                  className={cn(
                    "text-sm",
                    isActive ? "text-white/80" : "text-muted-foreground"
                  )}
                >
                  {displayName}
                </p>
              </div>
            </div>
            <div className="flex mt-2 items-center gap-2">
              <div className="flex items-center gap-2 text-xs">
                {chat.status === "pending" ? (
                  <>
                    <Clock className="h-3.5 w-3.5" />
                    <span>In behandeling</span>
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="h-3.5 w-3.5" />
                    <span>Geaccepteerd</span>
                  </>
                )}
              </div>
              <div className="text-xs whitespace-nowrap opacity-70">
                {formattedDate}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
