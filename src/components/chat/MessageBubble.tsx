import { format } from "date-fns";
import { nl } from "date-fns/locale";
import { File } from "lucide-react";

import { Message } from "@/types/chat";
import { cn } from "@/lib/utils";

interface MessageBubbleProps {
  message: Message;
  isCurrentUser: boolean;
}

export const MessageBubble = ({
  message,
  isCurrentUser,
}: MessageBubbleProps) => {
  const senderName =
    message.sender?.company_name ||
    message.sender?.full_name ||
    `${message.sender?.first_name || ""} ${
      message.sender?.last_name || ""
    }`.trim();

  return (
    <div
      className={cn(
        "flex gap-3",
        isCurrentUser ? "flex-row-reverse" : "flex-row"
      )}
    >
      <div
        className={cn(
          "max-w-[80%] rounded-2xl p-3 shadow-sm",
          isCurrentUser
            ? "bg-primary text-white rounded-tr-none"
            : "bg-muted rounded-tl-none"
        )}
      >
        <div className="flex flex-col gap-1">
          <div className="flex items-center justify-between gap-2">
            <span className="text-sm font-medium">{senderName}</span>
            <span className="text-xs opacity-70">
              {format(new Date(message.created_at), "HH:mm", { locale: nl })}
            </span>
          </div>
          <p className="mt-1 text-sm whitespace-pre-wrap">{message.content}</p>
          {message.attachment_url && (
            <div className="mt-2">
              {message.attachment_type === "image" ? (
                <img
                  src={message.attachment_url}
                  alt="Gedeelde afbeelding"
                  className="max-w-full rounded-lg"
                />
              ) : (
                <a
                  href={message.attachment_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={cn(
                    "flex items-center gap-2 p-2 rounded-lg",
                    isCurrentUser ? "bg-white/10" : "bg-background"
                  )}
                >
                  <File className="h-4 w-4" />
                  <span className="text-sm">Download bestand</span>
                </a>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
