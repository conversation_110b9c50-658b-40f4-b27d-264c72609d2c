import { Send, Image, File, X } from "lucide-react";
import { useState, useRef } from "react";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface MessageInputProps {
  disabled?: boolean;
  onSend: (message: string) => void;
  jobId: string;
  receiverId: string;
  chatId?: string;
}

export const MessageInput = ({
  onSend,
  disabled,
  jobId,
  receiverId,
  chatId,
}: MessageInputProps) => {
  const [value, setValue] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  };

  const handleSend = async () => {
    if (!value.trim() && selectedFiles.length === 0) return;

    try {
      setIsUploading(true);

      const shouldUploadFiles = selectedFiles.length > 0;
      const shouldSendMessage = value.trim();

      if (shouldUploadFiles) {
        const {
          data: { user },
          error: authError,
        } = await supabase.auth.getUser();
        if (authError || !user) throw new Error("Authentication failed");

        const uploadPromises = selectedFiles.map(async (file) => {
          const formData = new FormData();
          const payload = {
            file,
            jobId,
            senderId: user.id,
            receiverId,
            chat_id: chatId,
          };

          Object.entries(payload).forEach(([key, value]) => {
            formData.append(key, value);
          });

          const { error } = await supabase.functions.invoke(
            "upload-chat-attachment",
            {
              body: formData,
            }
          );

          if (error) throw error;
        });

        await Promise.all(uploadPromises);

        toast({
          title: "Bestanden geüpload",
          description: "Je bestanden zijn succesvol gedeeld in de chat.",
        });
      }

      if (shouldSendMessage) {
        onSend(value.trim());
      }

      // Reset state
      setValue("");
      setSelectedFiles([]);
    } catch (error) {
      console.error("[MessageInput] Upload error:", error);
      toast({
        variant: "destructive",
        title: "Upload mislukt",
        description: "Er ging iets mis bij het uploaden van je bestanden.",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (files: File[]) => {
    const validFiles = files.filter((file) => file.size <= 52428800);

    if (validFiles.length !== files.length) {
      toast({
        title: "Groottelimietfout",
        description:
          "Sommige bestanden zijn groter dan 50 MB en worden overgeslagen.",
      });
    }

    setSelectedFiles((prev) => [...prev, ...validFiles]);
    toast({
      title: "Bestanden geselecteerd",
      description:
        "Klik op versturen om het bericht met de bestanden te delen.",
    });
  };

  return (
    <div className="flex flex-col space-y-2">
      <div className="flex items-center gap-3 px-5 py-3 bg-gradient-to-r from-secondary/50 to-background rounded-xl border shadow-sm">
        <div className="flex items-center gap-2">
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept=".pdf,.doc,.docx,.txt"
            multiple
            onChange={(e) => {
              const files = Array.from(e.target.files || []);
              handleFileSelect(files);
            }}
          />
          <input
            type="file"
            ref={imageInputRef}
            className="hidden"
            accept="image/*"
            multiple
            onChange={(e) => {
              const files = Array.from(e.target.files || []);
              handleFileSelect(files);
            }}
          />
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              disabled={disabled || isUploading}
              onClick={() => fileInputRef.current?.click()}
              className="text-muted-foreground hover:text-foreground hover:bg-secondary transition-colors"
              title="Bestand toevoegen"
            >
              <File className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              disabled={disabled || isUploading}
              onClick={() => imageInputRef.current?.click()}
              className="text-muted-foreground hover:text-foreground hover:bg-secondary transition-colors"
              title="Afbeelding toevoegen"
            >
              <Image className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <Textarea
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="Type je bericht..."
          rows={1}
          className="min-h-[22px] max-h-[120px] flex-1 resize-none border-0 focus-visible:ring-0 p-2 bg-transparent placeholder:text-muted-foreground/60"
          disabled={disabled || isUploading}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              handleSend();
            }
          }}
        />
        <Button
          onClick={handleSend}
          variant="ghost"
          size="icon"
          className="text-primary disabled:opacity-100 hover:text-white hover:bg-primary transition-colors"
          disabled={
            disabled || isUploading || (!value.trim() && !selectedFiles.length)
          }
          title="Versturen"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
      {selectedFiles.length > 0 && (
        <div className="space-y-2">
          {selectedFiles.map((file, index) => (
            <div
              key={index}
              className="px-4 py-2 flex items-center gap-2 text-sm text-muted-foreground bg-secondary/50 backdrop-blur-sm rounded-lg animate-fade-in"
            >
              <File className="h-4 w-4" />
              <span className="flex-1 truncate">
                {file.name} ({formatFileSize(file.size)})
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-muted-foreground hover:text-foreground"
                onClick={() =>
                  setSelectedFiles((files) =>
                    files.filter((_, i) => i !== index)
                  )
                }
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
