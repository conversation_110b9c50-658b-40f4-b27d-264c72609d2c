import { useState, MouseE<PERSON> } from "react";
import { Briefcase } from "lucide-react";

import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import AdminJobDetailInfo from "../jobs/AdminJobDetailInfo";
import { formatDutchPhoneNumber } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import ConfirmModal from "@/components/modal/ConfirmModal";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";

interface AdminMessageCardProps {
  job: any;
  removeJob: (jobId: string) => void;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "open":
      return "bg-blue-500 hover:bg-blue-600";
    case "in_behandeling":
      return "bg-yellow-500 hover:bg-yellow-600";
    case "accepted":
      return "bg-green-500 hover:bg-green-600";
    case "completed":
      return "bg-green-700 hover:bg-green-800";
    case "cancelled":
      return "bg-red-500 hover:bg-red-600";
    default:
      return "bg-gray-500 hover:bg-gray-600";
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case "open":
      return "Open";
    case "in_behandeling":
      return "In behandeling";
    case "accepted":
      return "Geaccepteerd";
    case "completed":
      return "Afgerond";
    case "cancelled":
      return "Geannuleerd";
    default:
      return status;
  }
};

export const AdminMessageCard = ({ job, removeJob }: AdminMessageCardProps) => {
  const [acceptLoading, setAcceptLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [status, setStatus] = useState<string>(job?.status);
  const [openDetail, setOpenDetail] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);

  const { toast } = useToast();

  const acceptJob = async (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setAcceptLoading(true);
    try {
      const { error } = await supabase
        .from("jobs")
        .update({
          status: "open",
        })
        .eq("id", job.id);
      if (error) throw error;

      setStatus("open");
      sendNotificationsForNewJob();
    } catch (error) {
      console.error(error);
    } finally {
      setAcceptLoading(false);
    }
  };

  const handleDeleteClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    setDeleteLoading(true);
    try {
      const { error } = await supabase.from("jobs").delete().eq("id", job.id);
      if (error) throw error;

      removeJob(job.id);
      toast({
        title: "Baan verwijderen",
        description: "Het verwijderen van de taak is succesvol.",
      });
    } catch (error) {
      console.error(error);
      toast({
        title: "Fout bij verwijderen",
        description: "Er is iets misgegaan bij het verwijderen van de taak.",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
      setIsConfirmOpen(false);
    }
  };

  const sendNotificationsForNewJob = async () => {
    if (!job?.title || !job?.id) {
      console.error("Missing required job information");
      return;
    }

    try {
      // Fetch all vakman profiles with email and phone number
      const { data: vakmanProfiles, error: profilesError } = await supabase
        .from("profiles")
        .select("id, email, phone_number, services")
        .eq("user_type", "vakman")
        .not("email", "is", null);

      if (profilesError)
        throw new Error(
          `Failed to fetch vakman profiles: ${profilesError.message}`
        );

      if (!vakmanProfiles?.length) {
        console.warn("No vakman profiles found to send notifications to");
        return;
      }

      // Filter vakman profiles by matching services with job services
      let filteredVakmanProfiles = vakmanProfiles;

      if (
        job?.services &&
        Array.isArray(job.services) &&
        job.services.length > 0
      ) {
        filteredVakmanProfiles = vakmanProfiles.filter((profile) => {
          // Check if profile has services and if there's any overlap with job services
          if (
            !profile.services ||
            !Array.isArray(profile.services) ||
            profile.services.length === 0
          ) {
            return false;
          }

          // Return true if there's at least one matching service
          return profile.services.some((profileService) =>
            job.services.includes(profileService)
          );
        });
      }

      if (!filteredVakmanProfiles?.length) {
        console.warn(
          "No vakman profiles found with matching services for this job"
        );
        return;
      }

      const emailList = filteredVakmanProfiles.map(({ email }) => email);

      const phoneNumbersWithId = filteredVakmanProfiles
        .filter(({ phone_number }) => phone_number)
        .map(({ id, phone_number }) => ({
          id,
          phone_number: formatDutchPhoneNumber(phone_number),
        }))
        .filter(({ phone_number }) => phone_number !== null);

      // Send emails
      const emailContent = {
        to: emailList,
        subject: "Nieuwe klus beschikbaar",
        html: `
          <div style="font-family: Arial, sans-serif;">
            <h2>Nieuwe Klus Beschikbaar</h2>
            <h3>${job.title}</h3>
            <p>Er is een nieuwe klus beschikbaar op Klusgebied.</p>
            <p>
              <a href="https://klusgebied.nl/banen/${job.id}" 
                 style="background-color: #007bff; color: white; padding: 10px 20px; 
                        text-decoration: none; border-radius: 5px;">
                Bekijk Details
              </a>
            </p>
            <p>Met vriendelijke groet,<br>Team Klusgebied</p>
          </div>
        `,
      };

      // Default SMS content
      const normalSmsContent = `Nieuwe klus beschikbaar: ${job.title}. Bekijk de details op https://klusgebied.nl/banen/${job.id}`;

      // Direct request SMS content
      const directRequestSmsContent = `Je hebt een directe klusaanvraag: ${job.title}. Bekijk en accepteer direct op https://klusgebied.nl/banen/${job.id}`;

      // Helper to check if vakman id is in direct_request array
      const isDirectRequestFor = (vakmanId: string): boolean => {
        return (
          Array.isArray(job.direct_request) &&
          job.direct_request.includes(vakmanId)
        );
      };

      // Prepare notification promises
      const notificationPromises = [
        // Send email notification
        supabase.functions.invoke("send-email", { body: emailContent }),

        // Send SMS to each phone number, customizing for direct requests
        ...phoneNumbersWithId.map(({ id, phone_number }) => {
          const smsContent = isDirectRequestFor(id)
            ? directRequestSmsContent
            : normalSmsContent;

          return supabase.functions.invoke("send-sms", {
            body: {
              to: phone_number,
              message: smsContent,
            },
          });
        }),
      ];

      // Execute notifications in parallel
      const notifications = await Promise.all(notificationPromises);

      // Check for errors
      const errors = notifications.filter((result) => result.error);
      if (errors.length > 0) {
        console.error("Some notifications failed:", errors);
        throw new Error("Failed to send some notifications");
      }

      // Show success toast with notification counts
      toast({
        title: "Notificaties verzonden",
        description: `Notificaties verzonden naar ${
          emailList.length
        } vakmannen via e-mail${
          phoneNumbersWithId.length > 0
            ? ` en ${phoneNumbersWithId.length} vakmannen via SMS`
            : ""
        }.`,
        variant: "default",
      });
    } catch (error) {
      console.error("Failed to process notifications:", error);
      throw error;
    }
  };

  const responseCount = job?.job_responses?.length;

  return (
    <>
      <Card
        className="group overflow-hidden transition-all duration-300 hover:shadow-lg bg-card border-border/30 animate-fade-in cursor-pointer"
        onClick={() => setOpenDetail(true)}
      >
        <div className="flex flex-col h-full">
          <div className="flex flex-col">
            <div className="px-6 pt-6">
              <div className="flex items-center gap-2">
                <p className="text-lg font-semibold text-foreground dark:text-foreground group-hover:text-primary transition-colors line-clamp-2">
                  {job?.title}
                </p>
                {job?.is_bot && (
                  <Badge
                    variant="secondary"
                    className="bg-primary/10 text-primary hover:bg-primary/20 transition-colors px-2 py-0.5"
                  >
                    <div className="flex items-center gap-1.5">
                      <svg
                        className="w-3 h-3"
                        fill="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4zm-2 0c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2z" />
                      </svg>
                      <span className="text-xs font-medium">BOT</span>
                    </div>
                  </Badge>
                )}
              </div>
            </div>
            <div className="relative mt-4">
              {!job?.photos || job?.photos.length === 0 ? (
                <div className="h-48 bg-muted flex items-center justify-center">
                  <Briefcase className="w-10 h-10 text-muted-foreground/50" />
                </div>
              ) : (
                <Carousel className="h-48 w-full">
                  <CarouselContent>
                    {job?.photos.map((url: string, index: number) => (
                      <CarouselItem key={index}>
                        <div className="h-48 w-full relative group">
                          <img
                            src={url}
                            alt={`Foto ${index + 1} van de klus`}
                            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                            onError={(e) => {
                              console.error("Image failed to load:", url);
                              e.currentTarget.src = "/placeholder.svg";
                            }}
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </div>
                      </CarouselItem>
                    ))}
                  </CarouselContent>
                  {job?.photos.length > 1 && (
                    <>
                      <CarouselPrevious className="left-2" />
                      <CarouselNext className="right-2" />
                    </>
                  )}
                </Carousel>
              )}
              <div className="absolute top-0 right-0 p-4 z-10 flex flex-col gap-2 items-end">
                <Badge
                  className={`text-white px-3 py-1.5 font-medium shadow-lg backdrop-blur-sm ${getStatusColor(
                    status
                  )}`}
                >
                  {getStatusText(status)}
                </Badge>
                {status === "open" && responseCount > 0 && (
                  <Badge
                    variant="secondary"
                    className="bg-white/90 text-gray-800 px-3 py-1.5 font-medium whitespace-nowrap shadow-lg backdrop-blur-sm"
                  >
                    {responseCount} reactie{responseCount !== 1 ? "s" : ""}
                  </Badge>
                )}
              </div>
            </div>
          </div>
          <div className="p-6 flex flex-col flex-grow space-y-4">
            <p className="text-muted-foreground line-clamp-2 flex-grow">
              {job?.description}
            </p>
            <div className="flex sm:flex-row justify-between gap-2 sm:gap-4 flex-col">
              {status === "pending" && (
                <Button
                  className="text-white w-full"
                  disabled={acceptLoading}
                  onClick={acceptJob}
                >
                  Accepteren
                </Button>
              )}
              <Button
                variant="error"
                className="text-white w-full"
                disabled={deleteLoading}
                onClick={handleDeleteClick}
              >
                Verwijderen
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <Dialog open={openDetail} onOpenChange={() => setOpenDetail(false)}>
        <DialogContent className="sm:max-w-[825px] max-h-[calc(100vh-100px)] overflow-y-auto">
          <DialogTitle hidden />
          <AdminJobDetailInfo job={job} status={status} />
        </DialogContent>
      </Dialog>
      <ConfirmModal
        isOpen={isConfirmOpen}
        setIsOpen={setIsConfirmOpen}
        confirmHandler={handleConfirmDelete}
        loading={deleteLoading}
        title="Baan verwijderen"
        content="Weet je zeker dat je deze baan wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt."
        variant="danger"
        yesText="Verwijderen"
      />
    </>
  );
};
