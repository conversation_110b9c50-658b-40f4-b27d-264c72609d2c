import { format } from "date-fns";
import { nl } from "date-fns/locale";
import {
  LayoutGrid,
  TrendingUp,
  TrendingDown,
  Clock,
  Users,
} from "lucide-react";

import { Card } from "@/components/ui/card";
import { TableStats } from "./types";

interface TableStatsCardProps {
  tableName: string;
  stats: TableStats;
}

export const TableStatsCard = ({ tableName, stats }: TableStatsCardProps) => {
  const getGrowthIndicator = (todayCount: number, weekCount: number) => {
    const dailyAverage = weekCount / 7;
    if (todayCount > dailyAverage) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (todayCount < dailyAverage) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  return (
    <Card className="p-6 hover:shadow-lg transition-all duration-300 animate-fade-in bg-gradient-to-br from-card to-background border-primary/10">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <LayoutGrid className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold text-lg capitalize">
                {tableName.replace(/_/g, " ")}
              </p>
              <p className="text-2xl font-bold text-primary">
                {stats.count.toLocaleString()}
              </p>
            </div>
          </div>
          {getGrowthIndicator(stats.todayCount, stats.weekCount)}
        </div>

        <div className="grid grid-cols-2 gap-4 pt-2">
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>Vandaag</span>
            </div>
            <p className="font-semibold">{stats.todayCount}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <TrendingUp className="h-4 w-4" />
              <span>Deze week</span>
            </div>
            <p className="font-semibold">{stats.weekCount}</p>
          </div>
        </div>

        {stats.lastUpdated && (
          <div className="pt-2 border-t border-border/50">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Laatst bijgewerkt:</span>
            </div>
            <p className="text-sm font-medium">
              {format(new Date(stats.lastUpdated), "d MMMM yyyy 'om' HH:mm", {
                locale: nl,
              })}
            </p>
          </div>
        )}
      </div>
    </Card>
  );
};
