import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";

interface FilterSectionProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  filterType: string;
  setFilterType: (value: string) => void;
  sortBy: string;
  setSortBy: (value: string) => void;
}

export const FilterSection = ({
  searchTerm,
  setSearchTerm,
}: FilterSectionProps) => {
  return (
    <Card className="p-4 mb-6">
      <div className="space-y-3">
        <label className="text-sm text-muted-foreground block">Zoeken</label>
        <Input
          className="w-full"
          placeholder="Zoek op naam, adres, bedrijfsnaam..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
    </Card>
  );
};