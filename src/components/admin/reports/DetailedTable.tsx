import { useState } from "react";

import { Card } from "@/components/ui/card";
import { VakmanDetailsDialog } from "./VakmanDetailsDialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";

interface DetailedTableProps {
  filteredVakmannen: any[];
  isLoading: boolean;
}

export const DetailedTable = ({
  filteredVakmannen,
  isLoading,
}: DetailedTableProps) => {
  const [selectedVakman, setSelectedVakman] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [displayCount, setDisplayCount] = useState(50);

  const handleVakmanClick = (vakman: any) => {
    setSelectedVakman(vakman);
    setIsDialogOpen(true);
  };

  const handleShowMore = () => {
    setDisplayCount((prev) => prev + 50);
  };

  const displayedVakmannen = filteredVakmannen.slice(0, displayCount);

  return (
    <>
      <Card className="p-4">
        <h2 className="text-xl font-semibold mb-4">Gedetailleerd Overzicht</h2>
        <ScrollArea className="w-full">
          <div className="sm:min-w-[800px] sm:w-full w-[calc(100vw-80px)] overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-2">Vakman</th>
                  <th className="text-left py-3 px-2">Contact</th>
                  <th className="text-left py-3 px-2">KvK / BTW</th>
                  <th className="text-left py-3 px-2">Klussen</th>
                  <th className="text-left py-3 px-2">Rating</th>
                  <th className="text-left py-3 px-2">Reviews</th>
                  <th className="text-left py-3 px-2">Saldo</th>
                  <th className="text-left py-3 px-2">Lid Sinds</th>
                </tr>
              </thead>
              <tbody>
                {isLoading
                  ? // Skeleton rows
                    Array.from({ length: 6 }).map((_, idx) => (
                      <tr key={idx} className="border-b animate-pulse">
                        <td className="py-2 px-2">
                          <div className="h-6 w-32 bg-muted rounded" />
                        </td>
                        <td className="py-2 px-2">
                          <div className="space-y-2">
                            <div className="h-4 w-40 bg-muted rounded" />
                            <div className="h-3 w-24 bg-muted rounded" />
                          </div>
                        </td>
                        <td className="py-2 px-2">
                          <div className="space-y-2">
                            <div className="h-4 w-24 bg-muted rounded" />
                            <div className="h-4 w-24 bg-muted rounded" />
                          </div>
                        </td>
                        <td className="py-2 px-2">
                          <div className="h-4 w-8 bg-muted rounded" />
                        </td>
                        <td className="py-2 px-2">
                          <div className="h-4 w-8 bg-muted rounded" />
                        </td>
                        <td className="py-2 px-2">
                          <div className="h-4 w-8 bg-muted rounded" />
                        </td>
                        <td className="py-2 px-2">
                          <div className="h-4 w-16 bg-muted rounded" />
                        </td>
                        <td className="py-2 px-2">
                          <div className="h-4 w-24 bg-muted rounded" />
                        </td>
                      </tr>
                    ))
                  : displayedVakmannen?.map((vakman) => (
                      <tr
                        key={vakman.id}
                        className="border-b hover:bg-muted/50 cursor-pointer transition-colors"
                        onClick={() => handleVakmanClick(vakman)}
                      >
                        <td className="py-2 px-2">
                          <div className="font-medium">
                            {vakman.company_name ||
                              `${vakman.first_name} ${vakman.last_name}`}
                          </div>
                        </td>
                        <td className="py-2 px-2">
                          <div className="text-sm">{vakman.email}</div>
                          <div className="text-xs text-muted-foreground">
                            {vakman.phone_number}
                          </div>
                        </td>
                        <td className="py-2 px-2">
                          <div className="text-sm">
                            KvK: {vakman.kvk_number || "-"}
                          </div>
                          <div className="text-sm">
                            BTW: {vakman.btw_number || "-"}
                          </div>
                        </td>
                        <td className="py-2 px-2">{vakman.completed_jobs}</td>
                        <td className="py-2 px-2">
                          {vakman.average_rating?.toFixed(1)}
                        </td>
                        <td className="py-2 px-2">{vakman.total_reviews}</td>
                        <td className="py-2 px-2">
                          € {vakman.balance?.toFixed(2) || "0.00"}
                        </td>
                        <td className="py-2 px-2">
                          {new Date(vakman.created_at).toLocaleDateString(
                            "nl-NL"
                          )}
                        </td>
                      </tr>
                    ))}
              </tbody>
            </table>
          </div>
        </ScrollArea>
        {displayCount < filteredVakmannen.length && (
          <div className="mt-4 flex justify-center">
            <Button
              variant="outline"
              onClick={handleShowMore}
              className="w-full max-w-xs"
            >
              Toon meer vakmannen
            </Button>
          </div>
        )}
      </Card>

      <VakmanDetailsDialog
        vakman={selectedVakman}
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
      />
    </>
  );
};
