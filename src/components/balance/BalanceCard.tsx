import { useState } from "react";
import { Euro } from "lucide-react";

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { QuickTopUpButtons } from "./QuickTopUpButtons";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { SUPABASE_URL } from "@/integrations/supabase/client";

interface BalanceCardProps {
  balance: number | null;
  amount: string;
  setAmount: (amount: string) => void;
  isAddingFunds: boolean;
}

export const BalanceCard = ({
  balance,
  amount,
  setAmount,
  isAddingFunds,
}: BalanceCardProps) => {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleAddFunds = async () => {
    try {
      setIsProcessing(true);
      await validateAmount();
      const user = await getCurrentUser();
      const transaction = await createBalanceTransaction(user.id);
      const paymentUrl = await createMolliePayment(user.id, transaction.id);
      window.location.href = paymentUrl;
    } catch (error) {
      handleError(error);
    } finally {
      setIsProcessing(false);
    }
  };

  const validateAmount = async () => {
    const numericAmount = Number(amount);
    if (!amount || isNaN(numericAmount)) {
      throw new Error("Voer een geldig bedrag in.");
    }
    if (numericAmount < 0.01) {
      throw new Error("Het minimumbedrag is €0,01.");
    }
    if (numericAmount > 10000) {
      throw new Error("Het maximumbedrag is €10.000.");
    }
    return numericAmount;
  };

  const getCurrentUser = async () => {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    if (error || !user) {
      throw new Error("Je moet ingelogd zijn om saldo toe te voegen.");
    }
    return user;
  };

  const createBalanceTransaction = async (userId: string) => {
    const { data, error } = await supabase
      .from("balance_transactions")
      .insert({
        user_id: userId,
        amount: Number(amount),
        type: "deposit",
        description: "Saldo opwaarderen via Mollie",
        status: "pending",
      })
      .select()
      .single();

    if (error || !data) {
      throw new Error("Kon transactie niet aanmaken");
    }
    return data;
  };

  const createMolliePayment = async (userId: string, transactionId: string) => {
    const { data, error } = await supabase.functions.invoke(
      "create-mollie-payment",
      {
        body: {
          amount: Number(amount),
          userId,
          transactionId,
          description: `Saldo opwaarderen met €${Number(amount).toFixed(2)}`,
          redirectUrl: `${window.location.origin}/evenwicht?payment_status=success&transaction_id=${transactionId}`,
          webhookUrl: `${SUPABASE_URL}/functions/v1/mollie-webhook`,
        },
      }
    );

    if (error || !data?.paymentUrl) {
      throw new Error("Er ging iets mis bij het aanmaken van de betaling");
    }
    return data.paymentUrl;
  };

  const handleError = (error: unknown) => {
    console.error("Error adding funds:", error);
    toast({
      title: "Fout bij toevoegen saldo",
      description:
        error instanceof Error
          ? error.message
          : "Er ging iets mis bij het toevoegen van saldo. Probeer het later opnieuw.",
      variant: "destructive",
    });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="flex items-center gap-2 text-xl sm:text-2xl">
          <Euro className="h-5 w-5" />
          Beschikbaar Saldo
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <div className="text-3xl sm:text-4xl font-bold">
            €{balance?.toFixed(2)}
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Je kunt dit saldo gebruiken om te reageren op klussen. Elke reactie
            kost €5.
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium block">Bedrag (€)</label>
            <Input
              type="number"
              min="0.01"
              max="10000"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.00"
              disabled={isProcessing}
              className="text-lg p-4 h-auto"
            />
            <p className="text-xs text-muted-foreground">
              Minimum: €0,01 - Maximum: €10.000
            </p>
          </div>

          <QuickTopUpButtons
            selectedAmount={amount}
            onSelectAmount={setAmount}
            isAddingFunds={isProcessing}
          />

          <Button
            onClick={handleAddFunds}
            disabled={isProcessing || isAddingFunds}
            className="w-full py-6 text-lg"
          >
            {isProcessing ? "Bezig met verwerken..." : "Voeg saldo toe"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
