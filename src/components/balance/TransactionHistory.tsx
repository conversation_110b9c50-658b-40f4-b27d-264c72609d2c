import { useState } from "react";
import { History, FileDown, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { nl } from "date-fns/locale";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Transaction } from "@/types/database";
import { Button } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface TransactionHistoryProps {
  transactions: Transaction[];
}

export const TransactionHistory = ({
  transactions: originalTransactions,
}: TransactionHistoryProps) => {
  const { toast } = useToast();
  const [loadingPDFs, setLoadingPDFs] = useState<Set<string>>(new Set());

  const transactions = originalTransactions;

  const handleGeneratePDF = async (transactionId: string): Promise<void> => {
    if (loadingPDFs.has(transactionId)) return;

    setLoadingPDFs((prev) => new Set([...prev, transactionId]));

    try {
      const transaction = transactions.find((t) => t.id === transactionId);

      // Return early if PDF already exists
      if (transaction?.pdf_url) {
        window.open(transaction.pdf_url, "_blank", "noopener,noreferrer");
        return;
      }

      const { data, error } = await supabase.functions.invoke<{ url: string }>(
        "generate-transaction-pdf",
        {
          body: { transactionId },
        }
      );

      if (error) throw error;

      window.open(data.url, "_blank", "noopener,noreferrer");

      toast({
        title: "PDF gegenereerd",
        description: "Het transactiedocument is succesvol gegenereerd.",
      });
    } catch (error) {
      console.error("[PDF Generation Error]:", error);
      toast({
        title: "Fout bij genereren PDF",
        description:
          "Er is een fout opgetreden bij het genereren van het document.",
        variant: "destructive",
      });
    } finally {
      setLoadingPDFs((prev) => {
        const next = new Set(prev);
        next.delete(transactionId);
        return next;
      });
    }
  };

  const getTransactionDescription = (transaction: Transaction): string => {
    const { type, description } = transaction;
    const isBonus = description?.includes("Bonus");

    if (type === "deposit") {
      if (isBonus) return description;
      if (!description) return "Startbonus voor nieuwe gebruikers";
    }

    return (
      description ||
      (type === "deposit" ? "Saldo toegevoegd" : "Saldo gebruikt")
    );
  };

  return (
    <Card>
      <CardHeader className="pb-0">
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Transactiegeschiedenis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.length === 0 ? (
            <p className="text-sm text-muted-foreground py-4">
              Geen transacties gevonden.
            </p>
          ) : (
            transactions.map((transaction, idx) => (
              <div
                key={idx}
                className="flex sm:flex-row flex-col sm:items-center gap-2 sm:justify-between py-2 border-b last:border-0"
              >
                <div className="flex-1">
                  <p className="font-medium">
                    {getTransactionDescription(transaction)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(transaction.created_at), "PPP", {
                      locale: nl,
                    })}
                  </p>
                </div>
                <div className="flex flex-row items-center gap-2 flex-wrap">
                  <span className={"font-bold mr-4"}>
                    {transaction.type === "deposit" ? "+" : "-"}€
                    {Math.abs(transaction.amount).toFixed(2)}
                  </span>
                  <p
                    className={cn(
                      "text-sm border px-2 rounded-md",
                      transaction.status === "accepted"
                        ? "text-green-600 border-green-600 bg-green-600/10"
                        : "text-red-600 border-red-600 bg-red-600/10"
                    )}
                  >
                    {transaction.status === "accepted" ? "Success" : "Failed"}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-2"
                    onClick={() => handleGeneratePDF(transaction.id)}
                    disabled={loadingPDFs.has(transaction.id)}
                    aria-label="Download transactiedocument"
                  >
                    {loadingPDFs.has(transaction.id) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <FileDown className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};
