import { useState } from "react";
import { Loader2, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { PhotoUpload } from "./PhotoUpload";

export const AddProjectForm = ({ onSuccess }: { onSuccess: () => void }) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [budget, setBudget] = useState<string>("");
  const [photos, setPhotos] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) throw new Error("User authentication required");

      // Upload photos in parallel
      const uploadPromises = photos.map(async (photo) => {
        const fileExt = photo.name.split(".").pop();
        const fileName = `${crypto.randomUUID()}.${fileExt}`;

        const { error: uploadError, data } = await supabase.storage
          .from("job-photos")
          .upload(fileName, photo);

        if (uploadError) {
          throw new Error(`Failed to upload photo: ${uploadError.message}`);
        }

        const {
          data: { publicUrl },
        } = supabase.storage.from("job-photos").getPublicUrl(fileName);

        return {
          id: crypto.randomUUID(),
          photo_url: publicUrl,
        };
      });

      const uploadedPhotos = await Promise.all(uploadPromises);

      const parsedBudget = budget
        ? Number(budget.replace(/[^0-9.,]/g, "").replace(",", "."))
        : null;

      const { error: projectError } = await supabase
        .from("portfolio_projects")
        .insert({
          title,
          description,
          user_id: user.id,
          photos: uploadedPhotos,
          budget: parsedBudget,
        });

      if (projectError) {
        throw new Error(`Failed to create project: ${projectError.message}`);
      }

      toast({
        title: "Project toegevoegd",
        description: "Je project is succesvol toegevoegd aan je portfolio.",
      });

      // Reset form state
      const resetForm = () => {
        setTitle("");
        setDescription("");
        setBudget("");
        setPhotos([]);
        setPreviewUrls([]);
        onSuccess();
      };

      resetForm();
    } catch (error) {
      console.error("Error adding project:", error);
      toast({
        variant: "destructive",
        title: "Fout bij toevoegen project",
        description:
          error instanceof Error
            ? error.message
            : "Er is iets misgegaan bij het toevoegen van je project.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBudgetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBudget(e.target.value);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Input
          placeholder="Projecttitel"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
        />
      </div>

      <div>
        <Input
          placeholder="Budget (optioneel)"
          value={budget}
          onChange={handleBudgetChange}
          className="font-mono"
          type="number"
        />
      </div>

      <div>
        <Textarea
          placeholder="Beschrijf je project en het uitgevoerde werk"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          required
          className="min-h-[100px] resize-none"
        />
      </div>

      <PhotoUpload
        photos={photos}
        setPhotos={setPhotos}
        previewUrls={previewUrls}
        setPreviewUrls={setPreviewUrls}
      />

      <Button
        type="submit"
        disabled={isSubmitting || photos.length === 0}
        className="w-full"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Project toevoegen...
          </>
        ) : (
          <>
            <Plus className="mr-2 h-4 w-4" />
            Project toevoegen
          </>
        )}
      </Button>
    </form>
  );
};
