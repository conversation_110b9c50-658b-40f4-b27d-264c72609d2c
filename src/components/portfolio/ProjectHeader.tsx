import { <PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON>2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { CardHeader, CardTitle } from "@/components/ui/card";

interface ProjectHeaderProps {
  title: string;
  isDeleting: boolean;
  onEdit: () => void;
  onDelete: () => void;
}

export const ProjectHeader = ({
  title,
  isDeleting,
  onEdit,
  onDelete,
}: ProjectHeaderProps) => {
  return (
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-lg">{title}</CardTitle>
      <div className="flex gap-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={onEdit}
          aria-label="Edit project"
        >
          <Pencil className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-red-500"
          onClick={onDelete}
          disabled={isDeleting}
          aria-label="Delete project"
        >
          {isDeleting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Trash2 className="h-4 w-4 text-destructive" />
          )}
        </Button>
      </div>
    </CardHeader>
  );
};
