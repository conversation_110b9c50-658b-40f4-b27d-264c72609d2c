import { UseFormReturn } from "react-hook-form";
import { Briefcase } from "lucide-react";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const categories = [
  "<PERSON><PERSON><PERSON><PERSON>",
  "Elektricien",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>hoon<PERSON>",
  "<PERSON>",
];

interface CategoryFieldProps {
  form: UseFormReturn<any>;
}

export const JobCategoryField = ({ form }: CategoryFieldProps) => {
  return (
    <>
      <div className="flex items-center gap-2 mb-6">
        <Briefcase className="w-5 h-5 text-purple-500" />
        <h2 className="text-lg font-semibold">Categorie</h2>
      </div>

      <FormField
        control={form.control}
        name="category"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Type Klus</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Selecteer een type klus" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};
