/**
 * @description This component creates the final call-to-action section with compelling messaging, dual conversion paths and stunning visual effects. It is now fully reusable, accepting props for all text, buttons, images, trust elements, and statistics. The component includes a beautiful gradient background, animated elements, and strategic placement to maximize conversion rates. Key variables include props for customization and state for visibility-triggered animations.
 */
import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
	ArrowRight,
	UserPlus,
	Sparkles,
	CheckCircle,
	Clock,
	Shield,
} from "lucide-react";

const FinalCTA = ({
	backgroundImageUrl = "https://images.unsplash.com/photo-1673526759317-be71a1243e3d?ixid=M3w2MjE1MDB8MHwxfHNlYXJjaHwxfHxncmFkaWVudCUyQyUyMGFic3RyYWN0fGVufDB8fHx8MTc1MTc3MDI1N3ww&ixlib=rb-4.1.0&w=1920&h=1080",
	badgeText = "Start vandaag nog",
	title = "Klaar om jouw klus te klaren?",
	subtitle = "Word onderdeel van duizenden tevreden klanten en professionals die Klusgebied vertrouwen. Meer dan 2600 vakmannen actief – ook in jouw buurt.",
	primaryCta = { text: "Plaats direct je klus", path: "/" },
	secondaryCta = { text: "Ik ben een vakman", path: "/vakman" },
	trustElements = [
		{
			icon: CheckCircle,
			title: "100% Gratis",
			description: "Klus plaatsen kost niets",
			color: "text-teal-300",
		},
		{
			icon: Clock,
			title: "24/7 Support",
			description: "Altijd hulp wanneer nodig",
			color: "text-blue-300",
		},
		{
			icon: Shield,
			title: "Geld-terug",
			description: "100% tevredenheidsgarantie",
			color: "text-green-300",
		},
	],
	stats = [
		{ value: "15.000+", label: "Afgeronde klussen", color: "text-teal-400" },
		{ value: "4.8/5", label: "Gemiddelde score", color: "text-blue-400" },
		{ value: "2.600+", label: "Actieve vakmannen", color: "text-green-400" },
	],
}) => {
	const [isVisible, setIsVisible] = useState(false);
	const sectionRef = useRef();
	const navigate = useNavigate();

	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				if (entry.isIntersecting) {
					setIsVisible(true);
				}
			},
			{ threshold: 0.2 }
		);

		if (sectionRef.current) {
			observer.observe(sectionRef.current);
		}

		return () => {
			if (sectionRef.current) {
				observer.unobserve(sectionRef.current);
			}
		};
	}, []);

	const handleNavigation = (path) => {
		navigate(path);
	};

	return (
		<section
			ref={sectionRef}
			className="relative py-20 sm:py-24 lg:py-32 overflow-hidden"
		>
			<img
				src={backgroundImageUrl}
				alt="Abstract gradient background"
				className="absolute inset-0 w-full h-full object-cover"
				loading="lazy"
			/>
			<div className="absolute inset-0 bg-slate-900/70"></div>

			<div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
				{/* Main Content */}
				<div
					className={`text-center mb-12 md:mb-16 transition-all duration-1000 ${
						isVisible
							? "motion-preset-slide-up opacity-100"
							: "opacity-0 translate-y-10"
					}`}
				>
					{/* Badge */}
					<div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8">
						<Sparkles className="w-5 h-5 text-teal-300" />
						<span className="text-white font-semibold">{badgeText}</span>
					</div>

					<h2 className="text-4xl lg:text-6xl font-bold text-white mb-8 leading-tight">
						{title}
					</h2>

					<p className="text-xl lg:text-2xl text-slate-200 max-w-4xl mx-auto leading-relaxed">
						{subtitle}
					</p>
				</div>

				{/* CTA Buttons */}
				<div
					className={`flex flex-col sm:flex-row gap-6 justify-center items-center mb-16 md:mb-20 transition-all duration-1000 delay-300 ${
						isVisible
							? "motion-preset-slide-up opacity-100"
							: "opacity-0 translate-y-10"
					}`}
				>
					{/* Primary CTA */}
					<button
						onClick={() => handleNavigation(primaryCta.path)}
						className="group relative bg-gradient-to-r from-teal-500 to-teal-600 text-white px-10 lg:px-16 py-5 lg:py-6 rounded-2xl font-bold text-xl lg:text-2xl hover:from-teal-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-teal-500/20 flex items-center space-x-4 overflow-hidden hover:-translate-y-1"
					>
						<span className="relative z-10">{primaryCta.text}</span>
						<ArrowRight className="relative z-10 w-6 h-6 transition-transform duration-300 group-hover:translate-x-2" />
						<div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out"></div>
					</button>

					{/* Secondary CTA */}
					<button
						onClick={() => handleNavigation(secondaryCta.path)}
						className="group relative bg-white/10 border-2 border-white/50 text-white px-10 lg:px-16 py-5 lg:py-6 rounded-2xl font-bold text-xl lg:text-2xl hover:bg-white/90 hover:text-slate-800 hover:border-white transition-all duration-300 shadow-lg hover:shadow-xl flex items-center space-x-4 backdrop-blur-sm hover:-translate-y-1"
					>
						<UserPlus className="w-6 h-6 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-6" />
						<span>{secondaryCta.text}</span>
					</button>
				</div>

				{/* Trust Elements */}
				<div
					className={`grid grid-cols-1 md:grid-cols-3 gap-8 pt-12 md:pt-16 border-t border-white/20 transition-all duration-1000 delay-600 ${
						isVisible
							? "motion-preset-slide-up opacity-100"
							: "opacity-0 translate-y-10"
					}`}
				>
					{trustElements.map((element, index) => {
						const Icon = element.icon;
						return (
							<div
								key={element.title}
								className={`text-center group transition-all duration-500 hover:scale-105 motion-preset-slide-up motion-delay-${
									(index + 1) * 200
								}`}
							>
								<div className="flex justify-center mb-4">
									<div className="p-4 bg-white/10 backdrop-blur-sm rounded-2xl group-hover:bg-white/20 transition-all duration-300 group-hover:scale-110">
										<Icon
											className={`w-8 h-8 ${element.color} group-hover:scale-110 transition-transform duration-300`}
										/>
									</div>
								</div>
								<div className="text-2xl lg:text-3xl font-bold text-white mb-2 group-hover:text-teal-300 transition-colors duration-300">
									{element.title}
								</div>
								<div className="text-slate-200 font-medium group-hover:text-white transition-colors duration-300">
									{element.description}
								</div>
							</div>
						);
					})}
				</div>

				{/* Bottom Stats */}
				<div
					className={`text-center mt-16 md:mt-20 transition-all duration-1000 delay-800 ${
						isVisible
							? "motion-preset-slide-up opacity-100"
							: "opacity-0 translate-y-10"
					}`}
				>
					<div className="inline-flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-8 bg-white/5 backdrop-blur-sm rounded-2xl px-6 sm:px-8 py-6 border border-white/10">
						{stats.map((stat, index) => (
							<React.Fragment key={stat.label}>
								<div className="text-center">
									<div
										className={`text-xl md:text-2xl font-bold ${stat.color}`}
									>
										{stat.value}
									</div>
									<div className="text-slate-300 text-sm">{stat.label}</div>
								</div>
								{index < stats.length - 1 && (
									<div className="w-px h-8 bg-slate-600/50 hidden sm:block"></div>
								)}
							</React.Fragment>
						))}
					</div>
				</div>
			</div>
		</section>
	);
};

export default FinalCTA;
