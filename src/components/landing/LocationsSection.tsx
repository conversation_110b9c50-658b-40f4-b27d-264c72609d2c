/**
 * @description This component displays a comprehensive list of cities and regions where Klusgebied operates with interactive hover effects and animations.
 * It organizes locations into multiple columns with clickable links for each city to improve geographic targeting and user navigation.
 * The component features responsive design, staggered animations, hover effects, and maintains clean typography and accessibility standards.
 * Key variables include locationsData organized by provinces, handleLocationClick function with proper navigation routing, and interactive card hover states for enhanced UX.
 */
import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { MapPin, Navigation, Sparkles } from "lucide-react";

const LocationsSection = () => {
	const [hoveredProvince, setHoveredProvince] = useState(null);
	const [isVisible, setIsVisible] = useState(false);
	const sectionRef = useRef();
	const navigate = useNavigate();

	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				if (entry.isIntersecting) {
					setIsVisible(true);
				}
			},
			{ threshold: 0.1 }
		);

		if (sectionRef.current) {
			observer.observe(sectionRef.current);
		}

		return () => observer.disconnect();
	}, []);

	const locationsData = {
		"Noord-Holland": {
			cities: [
				"Amsterdam",
				"Haarlem",
				"Alkmaar",
				"Hilversum",
				"Amstelveen",
				"Hoofddorp",
				"Zaandam",
				"Purmerend",
				"Hoorn",
				"Den Helder",
			],
			count: "850+",
			color: "from-blue-500 to-cyan-500",
		},
		"Zuid-Holland": {
			cities: [
				"Rotterdam",
				"Den Haag",
				"Leiden",
				"Dordrecht",
				"Zoetermeer",
				"Delft",
				"Gouda",
				"Alphen aan den Rijn",
				"Spijkenisse",
				"Katwijk",
			],
			count: "920+",
			color: "from-teal-500 to-green-500",
		},
		Utrecht: {
			cities: [
				"Utrecht",
				"Nieuwegein",
				"Veenendaal",
				"Amersfoort",
				"Zeist",
				"Woerden",
				"IJsselstein",
				"Houten",
				"De Bilt",
				"Bunnik",
			],
			count: "680+",
			color: "from-purple-500 to-pink-500",
		},
		Gelderland: {
			cities: [
				"Arnhem",
				"Nijmegen",
				"Apeldoorn",
				"Ede",
				"Zutphen",
				"Doetinchem",
				"Tiel",
				"Wageningen",
				"Harderwijk",
				"Barneveld",
			],
			count: "540+",
			color: "from-orange-500 to-red-500",
		},
		"Noord-Brabant": {
			cities: [
				"Eindhoven",
				"Tilburg",
				"Breda",
				"Den Bosch",
				"Helmond",
				"Roosendaal",
				"Bergen op Zoom",
				"Oss",
				"Oosterhout",
				"Uden",
			],
			count: "720+",
			color: "from-indigo-500 to-blue-500",
		},
		Overijssel: {
			cities: [
				"Enschede",
				"Zwolle",
				"Deventer",
				"Almelo",
				"Hengelo",
				"Kampen",
				"Hardenberg",
				"Emmen",
				"Oldenzaal",
				"Steenwijk",
			],
			count: "380+",
			color: "from-emerald-500 to-teal-500",
		},
	};

	const handleLocationClick = (city, province) => {
		// Navigate to city-specific page
		const citySlug = city.toLowerCase().replace(/\s+/g, "-");
		navigate(`/stad/${citySlug}`);
	};

	return (
		<section
			ref={sectionRef}
			className="py-16 sm:py-20 lg:py-28 bg-gradient-to-br from-slate-50 via-white to-slate-50 relative overflow-hidden"
		>
			{/* Background Elements */}
			<div className="absolute top-20 left-20 opacity-20">
				<Navigation className="w-32 h-32 text-teal-500 motion-preset-float" />
			</div>
			<div className="absolute bottom-20 right-20 opacity-10">
				<Sparkles className="w-24 h-24 text-blue-500 motion-preset-float motion-delay-700" />
			</div>

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
				<div
					className={`text-center mb-12 md:mb-20 transition-all duration-1000 ${
						isVisible
							? "motion-preset-slide-up opacity-100"
							: "opacity-0 translate-y-10"
					}`}
				>
					<div className="inline-flex items-center space-x-3 bg-teal-50 rounded-full px-6 py-3 mb-8">
						<MapPin className="w-6 h-6 text-teal-600" />
						<span className="text-teal-800 font-semibold">
							Landelijk netwerk
						</span>
					</div>

					<h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
						Overal in Nederland een vakman in de buurt
					</h2>
					<p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
						Van Amsterdam tot Maastricht - onze vakmannen zijn actief in alle
						grote steden en regio's.
					</p>
				</div>

				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
					{Object.entries(locationsData).map(
						([province, data], provinceIndex) => (
							<div
								key={province}
								className={`relative group transition-all duration-700 ${
									isVisible
										? `motion-preset-slide-up motion-delay-${
												provinceIndex * 150
										  }`
										: "opacity-0 translate-y-10"
								}`}
								onMouseEnter={() => setHoveredProvince(province)}
								onMouseLeave={() => setHoveredProvince(null)}
							>
								<div
									className={`bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-slate-100 hover:border-slate-200 relative overflow-hidden ${
										hoveredProvince === province
											? "scale-105 -translate-y-2"
											: ""
									}`}
								>
									{/* Header */}
									<div className="relative z-10 mb-8">
										<div className="flex items-center justify-between mb-4">
											<h3 className="text-xl md:text-2xl font-bold text-slate-800">
												{province}
											</h3>
											<div
												className={`bg-gradient-to-r ${data.color} text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg`}
											>
												{data.count} vakmannen
											</div>
										</div>
										<div className="w-full h-px bg-gradient-to-r from-slate-300 via-slate-200 to-transparent"></div>
									</div>

									{/* Cities Grid */}
									<div className="relative z-10 grid grid-cols-2 gap-3">
										{data.cities.map((city, cityIndex) => (
											<button
												key={city}
												onClick={() => handleLocationClick(city, province)}
												className={`text-left text-slate-700 font-semibold bg-slate-50 border border-slate-200 shadow-sm px-4 py-3 rounded-xl transition-all duration-300 text-sm hover:bg-teal-500 hover:text-white hover:shadow-lg hover:border-teal-300 transform hover:-translate-y-0.5 group/city ${
													hoveredProvince === province
														? `motion-preset-slide-right motion-delay-${
																cityIndex * 50
														  }`
														: ""
												}`}
											>
												<div className="flex items-center space-x-2">
													<div className="w-2 h-2 bg-slate-400 rounded-full group-hover/city:bg-white transition-colors duration-300"></div>
													<span>{city}</span>
												</div>
											</button>
										))}
									</div>

									{/* Background Gradient Effect */}
									<div
										className={`absolute inset-0 bg-gradient-to-br ${data.color} opacity-0 group-hover:opacity-5 transition-all duration-500 rounded-3xl pointer-events-none`}
									></div>

									{/* Corner Decoration */}
									<div
										className={`absolute top-4 right-4 w-12 h-12 bg-gradient-to-br ${data.color} rounded-full opacity-10 group-hover:opacity-20 transition-all duration-500`}
									></div>
								</div>
							</div>
						)
					)}
				</div>

				{/* Bottom CTA */}
				<div
					className={`text-center mt-12 md:mt-20 transition-all duration-1000 delay-800 ${
						isVisible
							? "motion-preset-slide-up opacity-100"
							: "opacity-0 translate-y-10"
					}`}
				>
					<div className="bg-gradient-to-r from-teal-500 to-blue-600 rounded-3xl p-6 sm:p-8 lg:p-12 text-white shadow-2xl relative overflow-hidden">
						{/* Background Pattern */}
						<div className="absolute inset-0 opacity-10">
							<div
								className="absolute inset-0"
								style={{
									backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
								}}
							></div>
						</div>

						<div className="relative z-10">
							<h3 className="text-2xl lg:text-3xl font-bold mb-4">
								Zie je jouw stad niet? Geen probleem!
							</h3>
							<p className="text-base md:text-lg opacity-90 mb-8 max-w-2xl mx-auto">
								We breiden ons netwerk continu uit. Plaats je klus en we zorgen
								dat je de juiste vakman vindt.
							</p>
							<a
								href="https://klusgebied.nl/plaats-een-klus"
								target="_blank"
								rel="noopener noreferrer"
								className="inline-flex items-center bg-white text-teal-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-slate-50 transition-all duration-300 shadow-md hover:shadow-lg hover:-translate-y-0.5 space-x-3"
							>
								<span>Plaats direct een klus</span>
								<svg
									className="w-5 h-5"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 5l7 7-7 7"
									/>
								</svg>
							</a>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default LocationsSection;
