/**
 * @description This component creates the main hero section for the Klusgebied homepage with a stunning visual background and dynamic search functionality. It is now fully reusable, accepting props for all text content, images, and informational points. The component includes smooth fade-in animations, dynamic service filtering, and responsive design that adapts beautifully across all device sizes. Key variables include props for customization and state for the interactive search functionality.
 */
import React, { useState, useEffect } from "react";
import { Search, MapPin, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { supabase } from "../lib/supabase";

const Hero = ({
	badgeText = "Nederland's #1 vakmannen platform",
	title = (
		<>
			Vind snel een vakman voor jouw klus
			<span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-500">
				Klusgebied.nl
			</span>
		</>
	),
	subtitle = "Geverifieerde professionals voor elke klus in huis. Ontvang reacties van vakmannen binnen 24 uur.",
	searchPlaceholder = "Postcode en klus (bijv. 1011AB, loodgieter)",
	searchButtonText = "Zoe<PERSON>",
	infoPoints = [
		"Vraag gratis offertes aan",
		"Meer dan 2600 vakmannen actief",
		"Ook in jouw buurt",
	],
	heroImageUrl = "https://heyboss.heeyo.ai/user-assets/Ontwerp zonder titel (2)_ps3zDgB6.png",
	heroImageAlt = "Klusgebied App op iPhone",
}) => {
	const [searchTerm, setSearchTerm] = useState("");
	const [isVisible, setIsVisible] = useState(false);
	const [services, setServices] = useState([]);
	const [filteredServices, setFilteredServices] = useState([]);
	const [showDropdown, setShowDropdown] = useState(false);
	const [selectedIndex, setSelectedIndex] = useState(-1);
	const navigate = useNavigate();

	useEffect(() => {
		setIsVisible(true);
		fetchServices();
	}, []);

	// Fetch services from database
	const fetchServices = async () => {
		try {
			const { data, error } = await supabase
				.from("6865560a10605c25f99173ec_services")
				.select("id, name, slug, icon, category")
				.order("category", { ascending: false }) // Popular first
				.order("name", { ascending: true });

			if (error) {
				console.error("Error fetching services:", error);
				return;
			}

			setServices(data || []);
		} catch (error) {
			console.error("Error fetching services:", error);
		}
	};

	// Filter services based on search term
	useEffect(() => {
		if (searchTerm.trim().length > 0) {
			const filtered = services.filter((service) =>
				service.name.toLowerCase().includes(searchTerm.toLowerCase())
			);
			setFilteredServices(filtered);
			setShowDropdown(filtered.length > 0);
			setSelectedIndex(-1);
		} else {
			setFilteredServices([]);
			setShowDropdown(false);
			setSelectedIndex(-1);
		}
	}, [searchTerm, services]);

	// Handle input change
	const handleInputChange = (e) => {
		setSearchTerm(e.target.value);
	};

	// Handle service selection
	const handleServiceSelect = (service) => {
		setSearchTerm("");
		setShowDropdown(false);
		navigate(`/diensten/${service.slug}`);
	};

	// Handle keyboard navigation
	const handleKeyDown = (e) => {
		if (!showDropdown || filteredServices.length === 0) return;

		if (e.key === "ArrowDown") {
			e.preventDefault();
			setSelectedIndex((prev) =>
				prev < filteredServices.length - 1 ? prev + 1 : 0
			);
		} else if (e.key === "ArrowUp") {
			e.preventDefault();
			setSelectedIndex((prev) =>
				prev > 0 ? prev - 1 : filteredServices.length - 1
			);
		} else if (e.key === "Enter") {
			e.preventDefault();
			if (selectedIndex >= 0 && selectedIndex < filteredServices.length) {
				handleServiceSelect(filteredServices[selectedIndex]);
			} else {
				handleSearch(e);
			}
		} else if (e.key === "Escape") {
			setShowDropdown(false);
			setSelectedIndex(-1);
		}
	};

	// Handle input focus
	const handleInputFocus = () => {
		if (searchTerm.trim().length > 0 && filteredServices.length > 0) {
			setShowDropdown(true);
		}
	};

	// Handle input blur (with delay to allow clicks)
	const handleInputBlur = () => {
		setTimeout(() => {
			setShowDropdown(false);
			setSelectedIndex(-1);
		}, 200);
	};

	const handleSearch = (e) => {
		e.preventDefault();
		// If there's a search term, try to find matching service
		if (searchTerm.trim()) {
			const matchingService = services.find((service) =>
				service.name.toLowerCase().includes(searchTerm.toLowerCase())
			);

			if (matchingService) {
				navigate(`/diensten/${matchingService.slug}`);
			} else {
				// Fallback: scroll to services section
				document
					.getElementById("diensten")
					?.scrollIntoView({ behavior: "smooth" });
			}
		} else {
			// No search term: scroll to services section
			document
				.getElementById("diensten")
				?.scrollIntoView({ behavior: "smooth" });
		}

		// Clear search and hide dropdown
		setSearchTerm("");
		setShowDropdown(false);
	};

	return (
		<section className="bg-white pt-24 pb-16 md:pt-32 lg:pt-40 lg:pb-28 overflow-hidden">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
					{/* Left column: Text and Search */}
					<div className="text-center lg:text-left motion-preset-slide-right">
						<div
							className={`transition-all duration-1000 ${
								isVisible
									? "motion-preset-slide-up opacity-100"
									: "opacity-0 translate-y-10"
							}`}
						>
							{/* Badge */}
							<div className="inline-flex items-center space-x-2 bg-teal-100 border border-teal-200/80 rounded-full px-4 py-2 mb-6 md:mb-8">
								<Sparkles className="w-4 h-4 text-teal-600" />
								<span className="text-teal-800 font-semibold text-sm">
									{badgeText}
								</span>
							</div>

							<h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-slate-900 leading-tight mb-6">
								{title}
							</h1>

							<p className="text-lg sm:text-xl text-slate-600 md:mb-12 leading-relaxed max-w-xl mx-auto lg:mx-0 mb-[177px]">
								{subtitle}
							</p>
						</div>

						{/* Enhanced Search Bar */}
						<div
							className={`transition-all duration-1000 delay-300 ${
								isVisible
									? "motion-preset-slide-up opacity-100"
									: "opacity-0 translate-y-10"
							}`}
						>
							<form onSubmit={handleSearch} className="relative group max-w-xl">
								<div className="relative flex flex-col sm:flex-row bg-white rounded-2xl shadow-lg overflow-hidden border border-slate-200/80 hover:shadow-xl transition-all duration-500 mt-[43px]">
									<div className="flex-1 relative">
										<MapPin className="absolute left-4 sm:left-6 top-1/2 transform -translate-y-1/2 text-slate-400 w-6 h-6 group-hover:text-teal-500 transition-colors duration-300" />
										<input
											type="text"
											placeholder={searchPlaceholder}
											value={searchTerm}
											onChange={handleInputChange}
											onKeyDown={handleKeyDown}
											onFocus={handleInputFocus}
											onBlur={handleInputBlur}
											className="w-full sm:pl-16 sm:pr-6 py-5 sm:py-6 text-lg text-slate-800 placeholder-slate-500 border-none focus:outline-none focus:ring-0 bg-transparent !pb-[0px] !pt-[24px] pl-[55px] pr-[24px]"
											autoComplete="off"
										/>

										{/* Dropdown Menu */}
										{showDropdown && filteredServices.length > 0 && (
											<div className="absolute top-full left-0 right-0 bg-white border border-slate-200 rounded-b-2xl shadow-2xl z-50 max-h-80 overflow-y-auto motion-preset-slide-down text-left">
												{filteredServices.map((service, index) => (
													<div
														key={service.id}
														onClick={() => handleServiceSelect(service)}
														className={`flex items-center space-x-4 px-4 sm:px-6 py-3 sm:py-4 hover:bg-slate-50 cursor-pointer transition-colors duration-200 border-b border-slate-100 last:border-b-0 ${
															index === selectedIndex
																? "bg-teal-50 border-teal-200"
																: ""
														}`}
													>
														<div className="flex-shrink-0">
															<div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
																<span className="text-teal-600 font-bold text-sm">
																	{service.name.charAt(0)}
																</span>
															</div>
														</div>
														<div className="flex-1">
															<div className="font-semibold text-slate-800">
																{service.name}
															</div>
															{service.category && (
																<div className="text-sm text-slate-500">
																	<span
																		className={`inline-block px-2 py-1 rounded-full text-xs ${
																			service.category === "Populair"
																				? "bg-orange-100 text-orange-700"
																				: "bg-slate-100 text-slate-600"
																		}`}
																	>
																		{service.category}
																	</span>
																</div>
															)}
														</div>
														<div className="flex-shrink-0">
															<svg
																className="w-5 h-5 text-slate-400"
																fill="none"
																stroke="currentColor"
																viewBox="0 0 24 24"
															>
																<path
																	strokeLinecap="round"
																	strokeLinejoin="round"
																	strokeWidth={2}
																	d="M9 5l7 7-7 7"
																/>
															</svg>
														</div>
													</div>
												))}
											</div>
										)}
									</div>
									<button
										type="submit"
										className="w-full sm:w-auto bg-gradient-to-r from-teal-500 to-teal-600 text-white px-6 sm:px-8 py-5 sm:py-6 font-bold text-lg hover:from-teal-600 hover:to-teal-700 transition-all duration-300 flex items-center justify-center space-x-3 hover:scale-105 shadow-lg hover:shadow-xl sm:rounded-l-none rounded-b-2xl sm:rounded-b-none sm:rounded-r-xl"
									>
										<Search className="w-6 h-6" />
										<span className="sm:inline">{searchButtonText}</span>
									</button>
								</div>

								{/* Search Enhancement Ring */}
								<div className="absolute inset-0 rounded-2xl ring-2 ring-teal-400/0 group-hover:ring-teal-400/30 transition-all duration-500 pointer-events-none"></div>
							</form>

							<div className="flex flex-wrap items-center justify-center lg:justify-start mt-8 gap-x-6 gap-y-4 sm:gap-x-8">
								{infoPoints.map((point, index) => (
									<div
										key={index}
										className="flex items-center space-x-2 text-slate-600"
									>
										<div
											className={`w-2 h-2 rounded-full animate-pulse ${
												["bg-green-400", "bg-teal-400", "bg-blue-400"][
													index % 3
												]
											}`}
										></div>
										<span className="text-sm font-medium">{point}</span>
									</div>
								))}
							</div>
						</div>
					</div>

					{/* Right column: iPhone Mockup */}
					<div className="relative hidden lg:flex items-center justify-center motion-preset-slide-up">
						<img
							src={heroImageUrl}
							alt={heroImageAlt}
							className="w-auto h-[467px] object-contain transition-transform duration-500 scale-[1.30] hover:scale-[1.35] drop-shadow-2xl"
							width="300"
							height="467"
							loading="lazy"
						/>
					</div>
				</div>
			</div>
		</section>
	);
};

export default Hero;
