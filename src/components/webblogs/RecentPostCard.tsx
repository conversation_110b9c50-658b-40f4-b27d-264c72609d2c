// src/components/RecentPostCard.js

import React from 'react';
import { Link } from 'react-router-dom';
import { urlFor } from '@/lib/sanity';

const RecentPostCard = ({ post }) => {
  // Make sure we have a post and its slug before rendering
  if (!post || !post.slug) return null;

  return (
    // Link the entire card to the blog post's page
    <Link to={`/weblogs/${post.slug.current}`} className="block mb-4 hover:bg-gray-100 p-2 rounded-lg">
      <div className="flex items-center">
        {post.mainImage ? (
          <img
            src={urlFor(post.mainImage).width(100).height(100).fit('crop').url()}
            alt={post.title}
            className="w-16 h-16 object-cover rounded-md shadow-sm"
          />
        ) : (
          <div className="w-16 h-16 bg-gray-200 rounded-md"></div>
        )}
        <h3 className="text-base font-medium text-gray-900 line-clamp-2 ml-4 flex-1">
          {post.title}
        </h3>
      </div>
    </Link>
  );
};

export default RecentPostCard;