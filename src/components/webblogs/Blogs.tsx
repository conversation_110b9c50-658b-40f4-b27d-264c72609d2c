// src/Blog.js
"use client";

import React, { useState, useEffect } from 'react';
import { client } from '@/lib/sanity';
import BlogPostCard from './BlogPostCard';

function Blogs() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");

  useEffect(() => {
    // === THIS QUERY HAS BEEN UPDATED ===
    // Instead of fetching the complex 'description' array, we use pt::text()
    // to create a new, simple 'plainDescription' string field.
    const query = `*[_type == "post"] | order(_createdAt desc){
      _id,
      title,
      slug,
      "plainDescription": pt::text(description), // <-- THE ONLY CHANGE IS HERE
      mainImage,
      "categories": categories[]->{
        _id,
        title
      }
    }`;

    client.fetch(query).then((data) => {
      setPosts(data);
      setLoading(false);
    }).catch((err) => {
      setError(err);
      setLoading(false);
    });
  }, []);

  // All of the filtering and rendering logic below remains exactly the same.
  // ... (the rest of your component code is correct and doesn't need to be changed) ...
  const categories = ["All", ...new Set(
    posts.flatMap(post => 
      post.categories ? post.categories.map(cat => cat.title) : []
    )
  )];

  const filteredPosts = posts.filter((post) => {
    const matchesSearch = post.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
      
    const matchesCategory =
      selectedCategory === "All" ||
      (post.categories && post.categories.some(cat => cat.title === selectedCategory));
      
    return matchesSearch && matchesCategory;
  });

  if (loading) return <div className="text-center text-white py-10">Loading posts...</div>;
  if (error) return <div className="text-center text-red-500 py-10">Error fetching posts: {error.message}</div>;

  return (
    <div className="min-h-screen mb-32 p-4 sm:p-8">
      <header className="text-center my-10 mb-12">
        <h1 className="text-4xl sm:text-3xl text-gray-900 font-semibold">Our Blogs</h1>
      </header>
      
      <div className="max-w-7xl mx-auto mb-12 flex flex-col sm:flex-row items-center justify-between gap-8">
        <div className="flex flex-wrap gap-4">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 text-sm font-medium rounded-full transition-colors duration-200 ${
                selectedCategory === category
                  ? "bg-primary text-white"
                  : "bg-gray-200 text-gray-800 hover:bg-gray-300"
              }`}
            >
              {category}
            </button>
          ))}
        </div>
        
        <div className="w-full sm:w-1/3">
          <input
            type="text"
            placeholder="Search by title..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-4 py-2 border border-primary text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
          />
        </div>
      </div>

      <main className="max-w-7xl mx-auto">
        {filteredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post) => (
              <BlogPostCard key={post._id} post={post} />
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-400 py-10">
            No posts found matching your criteria.
          </p>
        )}
      </main>
    </div>
  );
}

export default Blogs;