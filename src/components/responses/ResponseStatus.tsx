import { Check<PERSON>ircle, XCircle, Clock } from "lucide-react";

interface ResponseStatusProps {
  status: string;
}

export const ResponseStatus = ({ status }: ResponseStatusProps) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "accepted":
        return <CheckCircle className="w-5 h-5 text-success-DEFAULT" />;
      case "rejected":
      case "cancelled":
        return <XCircle className="w-5 h-5 text-red-500" />;
      case "pending":
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "accepted":
        return "Geaccepteerd";
      case "rejected":
        return "Afgewezen";
      case "cancelled":
        return "Niet geaccepteerd voor de klus";
      case "pending":
        return "In afwachting van klusplaatser";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {getStatusIcon(status)}
      <span className="font-medium">{getStatusText(status)}</span>
    </div>
  );
};