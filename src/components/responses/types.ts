import { Json } from '@/types/database/json';

export interface JobOwner {
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  phone_number: string | null;
  street_address: string | null;
  postal_code: string | null;
  city: string | null;
}

export interface Job {
  id: string;
  title: string;
  description: string;
  category?: string;
  job_type?: string;
  postal_code: string;
  house_number: string;
  house_number_addition: string | null;
  status: string | null;
  user_id: string;
  accepted_vakman_id?: string | null;
  created_at: string;
  budget: number | null;
  response_cost: number;
  deleted_at: string | null;
  photos: string[];
  details: Json | null;
  owner?: JobOwner;
}

export interface JobResponse {
  id: string;
  status: string;
  created_at: string;
  message: string | null;
  jobs: Job | null;
}