import { Clock, CheckCircle2, XCircle, AlertCircle } from "lucide-react";

import { Badge } from "@/components/ui/badge";

interface ResponseStatusBadgeProps {
  status: string;
}

export const getJobStatusBadge = (status: string) => {
  const statusConfig = {
    open: {
      color: "bg-blue-100 text-blue-800",
      icon: AlertCircle,
      label: "Open",
    },
    in_behandeling: {
      color: "bg-amber-100 text-amber-800",
      icon: Clock,
      label: "In behandeling",
    },
    accepted: {
      color: "bg-green-100 text-green-800",
      icon: CheckCircle2,
      label: "Geaccepteerd",
    },
    completed: {
      color: "bg-purple-100 text-purple-800",
      icon: CheckCircle2,
      label: "Afgerond",
    },
    cancelled: {
      color: "bg-red-100 text-red-800",
      icon: XCircle,
      label: "Geannuleerd",
    },
  } as const;

  const config = statusConfig[status as keyof typeof statusConfig] || {
    color: "bg-gray-100 text-gray-800",
    icon: AlertCircle,
    label: status.charAt(0).toUpperCase() + status.slice(1),
  };

  const Icon = config.icon;

  return (
    <Badge
      variant="secondary"
      className={`${config.color} flex items-center gap-1.5 px-3 py-1 rounded-full font-medium`}
    >
      <Icon className="w-4 h-4" />
      {config.label}
    </Badge>
  );
};

export const ResponseStatusBadge = ({ status }: ResponseStatusBadgeProps) => {
  return getJobStatusBadge(status);
};
