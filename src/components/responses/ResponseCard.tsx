import { useState, useEffect } from "react";
import { <PERSON><PERSON>qua<PERSON>, Star } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ResponseStatus } from "./ResponseStatus";
import { supabase } from "@/integrations/supabase/client";
import { ResponseDetailsDialog } from "./ResponseDetailsDialog";
import { getJobStatusBadge } from "./ResponseStatusBadge";
import type { JobOwner, JobResponse } from "./types";

interface ResponseCardProps {
  response: JobResponse;
}

export const ResponseCard = ({ response }: ResponseCardProps) => {
  const [showDetails, setShowDetails] = useState(false);
  const [jobOwner, setJobOwner] = useState<JobOwner | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchJobOwner = async () => {
      if (!response.jobs?.user_id) return;

      try {
        const { data: profile, error } = await supabase
          .from("profiles")
          .select(
            "first_name, last_name, email, phone_number, street_address, postal_code, city"
          )
          .eq("id", response.jobs.user_id)
          .single();

        if (error) {
          console.error("Error fetching job owner profile:", error);
          return;
        }

        setJobOwner(profile);
      } catch (error) {
        console.error("Error in fetchJobOwner:", error);
      }
    };

    fetchJobOwner();
  }, [response.jobs?.user_id]);

  const handleViewDetails = () => {
    if (response.jobs) {
      setShowDetails(true);
    }
  };

  const handleChatClick = () => {
    if (response.jobs?.id) {
      navigate(`/gesprekken?jobId=${response.jobs.id}`);
    }
  };

  const shouldShowJobStatus = response.status !== "cancelled";
  const isJobCompleted = response.jobs?.status === "completed";
  const isAccepted = response.status === "accepted";

  return (
    <>
      <Card
        className="group hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer"
        onClick={handleViewDetails}
      >
        <div className="p-4 space-y-3">
          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-2">
              <ResponseStatus status={response.status} />
              {shouldShowJobStatus &&
                response.jobs &&
                getJobStatusBadge(response.jobs.status)}
            </div>

            <div className="space-y-2">
              {response.jobs ? (
                <>
                  <p className="text-base font-medium group-hover:text-primary transition-colors">
                    {response.jobs.title}
                  </p>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {response.jobs.description}
                  </p>
                  {isAccepted && jobOwner && (
                    <div className="mt-4 space-y-2 text-sm text-muted-foreground border-t pt-2">
                      <h4 className="font-medium text-foreground">
                        Contactgegevens opdrachtgever:
                      </h4>
                      <p>
                        {jobOwner.first_name} {jobOwner.last_name}
                      </p>
                      {jobOwner.street_address && (
                        <p>
                          {jobOwner.street_address}
                          {response.jobs.house_number &&
                            ` ${response.jobs.house_number}`}
                          {response.jobs.house_number_addition &&
                            ` ${response.jobs.house_number_addition}`}
                        </p>
                      )}
                      <p>{response.jobs.postal_code}</p>
                      {jobOwner.city && <p>{jobOwner.city}</p>}
                      {jobOwner.phone_number && (
                        <p className="mt-1">Tel: {jobOwner.phone_number}</p>
                      )}
                      {jobOwner.email && <p>Email: {jobOwner.email}</p>}
                    </div>
                  )}
                </>
              ) : (
                <h3 className="text-base font-medium text-muted-foreground">
                  Klus niet meer beschikbaar
                </h3>
              )}
            </div>
          </div>

          {response.jobs && response.status === "accepted" && (
            <div className="flex items-center justify-end pt-3">
              <Button
                variant="default"
                size="sm"
                onClick={handleChatClick}
                className="font-medium shadow-sm hover:shadow-md transition-shadow bg-primary hover:bg-primary/90 text-white"
              >
                {isJobCompleted ? (
                  <>
                    <Star className="w-4 h-4 mr-2" />
                    Bekijk review
                  </>
                ) : (
                  <>
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Naar conversatie
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </Card>

      <ResponseDetailsDialog
        showDetails={showDetails}
        setShowDetails={setShowDetails}
        job={response.jobs}
        jobOwner={jobOwner}
        handleViewConversation={handleChatClick}
        getJobStatusBadge={getJobStatusBadge}
      />
    </>
  );
};
