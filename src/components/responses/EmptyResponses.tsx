import { MessageSquare } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

interface EmptyResponsesProps {
  onViewJobs: () => void;
}

export const EmptyResponses = ({ onViewJobs }: EmptyResponsesProps) => {
  return (
    <Card className="p-8 sm:p-12 text-center bg-muted/30">
      <div className="bg-primary/10 p-4 rounded-full w-fit mx-auto mb-6">
        <MessageSquare className="w-12 h-12 text-primary" />
      </div>
      <p className="text-xl font-semibold mb-3 text-accent">
        Nog geen reacties
      </p>
      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
        Je hebt nog niet gereageerd op klussen. Bekijk beschikbare klussen en
        begin met reageren.
      </p>
      <Button onClick={onViewJobs} size="lg" className="px-8">
        <PERSON><PERSON><PERSON> beschikbare klussen
      </Button>
    </Card>
  );
};
