import { MessageSquare } from "lucide-react";

import { BackToDashboard } from "@/components/BackToDashboard";

export const ResponseHeader = () => {
  return (
    <div className="space-y-6">
      <BackToDashboard />
      <div className="flex items-center gap-4 border-b pb-6">
        <div className="bg-primary/10 p-3 rounded-lg">
          <MessageSquare className="h-8 w-8 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-accent">
            Mijn Reacties
          </h1>
          <p className="text-muted-foreground mt-1 text-sm sm:text-base">
            <PERSON><PERSON><PERSON> hier al je reacties op klussen en hun status
          </p>
        </div>
      </div>
    </div>
  );
};
