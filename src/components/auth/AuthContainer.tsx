import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

import { Card } from "@/components/ui/card";
import { AuthForm } from "@/components/auth/AuthForm";
import { ViewType } from "@/types/auth";
import { AuthBenefitsSection } from "./sections/AuthBenefitsSection";
import { StatisticsSection } from "./sections/StatisticsSection";
import { HowItWorksSection } from "./sections/HowItWorksSection";
import { AdminLoginPage } from "@/pages/admin/AdminLoginPage";
import { ADMIN_DOMAIN } from "@/config/routes";

export const AuthContainer = () => {
  const [searchParams] = useSearchParams();

  const [view, setView] = useState<ViewType>("sign_in");

  useEffect(() => {
    const viewParam = searchParams.get("signup");
    if (!!viewParam) {
      setView("sign_up");
    }
  }, [searchParams]);

  if (
    typeof window !== "undefined" &&
    window.location.hostname === ADMIN_DOMAIN
  ) {
    return <AdminLoginPage />;
  }

  return (
    <div className="relative min-h-screen overflow-x-hidden">
      <div className="absolute inset-0 bg-white" />

      <div className="relative w-full">
        <div className="container mx-auto py-8 md:py-12 lg:py-16 px-6 sm:px-8 lg:px-12">
          <div className="flex flex-col lg:flex-row items-start justify-between gap-12 lg:gap-16 max-w-6xl mx-auto">
            <div className="text-black space-y-6 lg:w-5/12 text-left animate-fade-in">
              <h1 className="font-display text-3xl md:text-4xl lg:text-5xl font-semibold leading-tight tracking-tight text-black">
                De betrouwbare manier om opdrachten te vinden die bij je passen
              </h1>
              <p className="hidden lg:block text-xl text-black/90 leading-relaxed">
                Verbind direct met gekwalificeerde vakmensen in jouw buurt.
                Plaats je klus en ontvang snel reacties van betrouwbare
                professionals.
              </p>
              <div className="hidden lg:block">
                <div className="bg-gray-100 p-6 rounded-lg inline-block border border-gray-200">
                  <div className="flex items-center gap-4 justify-start">
                    <div className="text-yellow-500 text-2xl">★★★★★</div>
                    <div className="text-black">
                      <span className="font-semibold">4.8</span> uit 5
                      beoordelingen
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="lg:w-6/12 w-full max-w-md">
              <div className="relative w-screen -ml-[50vw] left-1/2 mb-8 lg:hidden">
                <img
                  src="/lovable-uploads/158acef8-3b1d-41a5-86bb-60cdb02d06be.png"
                  alt="Vakman aan het werk op een dak"
                  className="w-full h-72 object-cover shadow-xl"
                  loading="eager"
                />
              </div>

              <Card className="shadow-2xl bg-white/95 backdrop-blur p-8 rounded-2xl w-full border-t border-white/20 animate-fade-in">
                <AuthForm
                  view={view}
                  setView={setView}
                  onEmailSubmit={() => {}}
                />
              </Card>

              <div className="mt-8 lg:hidden">
                <div className="bg-gray-100 p-6 rounded-lg border border-gray-200 text-center">
                  <div className="flex items-center gap-4 justify-center">
                    <div className="text-yellow-500 text-2xl">★★★★★</div>
                    <div className="text-black">
                      <span className="font-semibold">4.8</span> uit 5
                      beoordelingen
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-24 pb-24">
        <AuthBenefitsSection />
        <StatisticsSection />
        <HowItWorksSection />
      </div>
    </div>
  );
};
