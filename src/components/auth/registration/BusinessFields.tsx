import { UseFormReturn } from "react-hook-form";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ProfileFormData } from "@/types/auth";

interface BusinessFieldsProps {
  form: UseFormReturn<ProfileFormData>;
}

export const BusinessFields = ({ form }: BusinessFieldsProps) => {
  return (
    <div className="space-y-4 border-t pt-4">
      <h3 className="font-medium">Bedrijfsgegevens</h3>
      <FormField
        control={form.control}
        name="company_name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Bedrijfsnaam</FormLabel>
            <FormControl>
              <Input placeholder="Uw Bedrijf B.V." {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="kvk_number"
        render={({ field }) => (
          <FormItem>
            <FormLabel>KvK <PERSON></FormLabel>
            <FormControl>
              <Input placeholder="12345678" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="btw_number"
        render={({ field }) => (
          <FormItem>
            <FormLabel>BTW Nummer</FormLabel>
            <FormControl>
              <Input placeholder="NL123456789B01" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
