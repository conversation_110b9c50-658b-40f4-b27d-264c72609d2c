import { Button } from "@/components/ui/button";

interface VerificationOptionsProps {
  onSelectOption: (option: 'password' | 'email' | 'phone') => void;
}

export const VerificationOptions = ({ onSelectOption }: VerificationOptionsProps) => {
  return (
    <div className="space-y-4">
      <Button 
        type="button"
        onClick={() => onSelectOption('password')}
        className="w-full bg-yellow-500 hover:bg-yellow-600 text-black focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
      >
        Wachtwoord invoeren
      </Button>
      <Button 
        type="button"
        onClick={() => onSelectOption('email')}
        className="w-full bg-yellow-500 hover:bg-yellow-600 text-black focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
      >
        Email verificatie
      </Button>
      <Button 
        type="button"
        onClick={() => onSelectOption('phone')}
        className="w-full bg-yellow-500 hover:bg-yellow-600 text-black focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
      >
        Telefoonnummer verificatie
      </Button>
    </div>
  );
};