export const AuthBenefitsSection = () => {
  return (
    <div className="relative bg-white py-24">
      <div className="container mx-auto px-6 sm:px-8 lg:px-12">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Waarom kiezen vakmannen voor ons platform?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Word onderdeel van ons groeiende netwerk van professionals en laat
            je bedrijf groeien
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="p-8 space-y-6 hover:shadow-lg transition-all duration-300">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-2xl text-primary">€</span>
            </div>
            <h3 className="text-xl font-semibold">Flexibele Verdiensten</h3>
            <p className="text-gray-600 leading-relaxed">
              Kies zelf welke klussen je aanneemt en bepaal je eigen tarieven.
              Geen vaste kosten, alleen een kleine fee per aangenomen klus.
            </p>
          </div>

          <div className="p-8 space-y-6 hover:shadow-lg transition-all duration-300">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-2xl text-primary">✓</span>
            </div>
            <h3 className="text-xl font-semibold">Flexibele Planning</h3>
            <p className="text-gray-600 leading-relaxed">
              Plan je werk wanneer het jou uitkomt. Beheer eenvoudig je agenda
              en beschikbaarheid via ons platform.
            </p>
          </div>

          <div className="p-8 space-y-6 hover:shadow-lg transition-all duration-300">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-2xl text-primary">★</span>
            </div>
            <h3 className="text-xl font-semibold">Direct Contact</h3>
            <p className="text-gray-600 leading-relaxed">
              Communiceer rechtstreeks met potentiële klanten via ons platform.
              Geen tussenpersonen, snelle en duidelijke communicatie.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
