import { ChangeEvent, useCallback, useEffect, useRef, useState } from "react";
import debounce from "lodash/debounce";
import { UseFormRegister, FieldErrors } from "react-hook-form";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ViewType } from "@/types/auth";

interface EmailStepProps {
  register: UseFormRegister<any>;
  errors: FieldErrors;
  view: ViewType;
  onExistingAccount?: () => void;
  onSubmit: (email: string) => void;
  onNewAccount?: () => void;
}

export const EmailStep = ({
  register,
  errors,
  view,
  onExistingAccount,
  onNewAccount,
}: EmailStepProps) => {
  const [isChecking, setIsChecking] = useState(false);
  const { toast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  // Create debounced validation function
  const debouncedValidateEmail = useCallback(
    debounce(
      async (email: string, resolve: (value: boolean | string) => void) => {
        // Abort previous request
        abortControllerRef.current?.abort();

        // Setup new request
        const controller = new AbortController();
        abortControllerRef.current = controller;

        setIsChecking(true);

        try {
          const { data: profile, error } = await supabase
            .from("profiles")
            .select("id, status")
            .eq("email", email)
            .abortSignal(controller.signal)
            .maybeSingle();

          if (error) throw error;

          if (!profile) {
            onNewAccount?.();
            resolve(true);
            return;
          }

          if (profile.status === "inactive") {
            toast({
              variant: "destructive",
              title: "Account geblokkeerd",
              description:
                "Dit account is geblokkeerd. Neem contact op met de klantenservice.",
            });
            resolve("inactive");
            return;
          }

          onExistingAccount?.();
          toast({
            variant: "destructive",
            title: "Email bestaat al",
            description:
              "Email adres al geregistreerd, log in of verander wachtwoord",
          });
          resolve("Dit emailadres heeft al een account");
        } catch (error) {
          if (error instanceof Error) {
            if (error.name === "AbortError") return;

            console.error("Email validation error:", error);
            toast({
              variant: "destructive",
              title: "Validatie fout",
              description:
                "Er is een fout opgetreden. Probeer het later opnieuw.",
            });
            resolve("Validatie fout opgetreden");
          }
        } finally {
          setIsChecking(false);
          if (abortControllerRef.current === controller) {
            abortControllerRef.current = null;
          }
        }
      },
      500
    ),
    [onExistingAccount, onNewAccount, toast]
  );

  // Wrapper function for React Hook Form
  const validateEmail = async (email: string) => {
    return new Promise<boolean | string>((resolve) => {
      debouncedValidateEmail(email, resolve);
    });
  };

  const handleChangeEmail = async (e: ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    if (email) {
      // Check email pattern first
      const emailPattern = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
      if (!emailPattern.test(email)) {
        return; // Don't proceed with validation if pattern is invalid
      }
      const result = await validateEmail(email);
      if (typeof result === "string") {
        if (result === "inactive") {
          e.target.value = ""; // Clear the input if the account is inactive
          return;
        }

        // Handle validation error
        toast({
          variant: "destructive",
          title: "Validatie fout",
          description: result,
        });
      }
    }
  };

  return (
    <div className="space-y-2">
      {view === "sign_up" && (
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Registreer je als vakman om klussen te zien
        </h2>
      )}
      <Label htmlFor="email">Email</Label>
      <Input
        id="email"
        type="email"
        placeholder="Je e-mailadres"
        {...register("email", {
          required: "Email is verplicht",
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: "Ongeldig email adres",
          },
          onChange: handleChangeEmail,
        })}
        isLoading={isChecking}
      />
      {errors.email && (
        <p className="text-sm text-red-500">{errors.email.message as string}</p>
      )}
    </div>
  );
};
