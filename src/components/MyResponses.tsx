import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Loader2 } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ResponseCard } from "./responses/ResponseCard";
import { EmptyResponses } from "./responses/EmptyResponses";
import { ResponseHeader } from "./responses/ResponseHeader";
import type { JobResponse } from "./responses/types";

export const MyResponses = () => {
  const [responses, setResponses] = useState<JobResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    const fetchResponses = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          return;
        }

        const { data: responseData, error: responseError } = await supabase
          .from("job_responses")
          .select(
            `
            *,
            jobs (
              *,
              owner:profiles!jobs_user_id_fkey (
                first_name,
                last_name,
                email,
                phone_number,
                street_address,
                house_number,
                house_number_addition,
                postal_code,
                city
              )
            )
          `
          )
          .eq("vakman_id", user.id)
          .order("created_at", { ascending: false });

        if (responseError) {
          console.error("Error fetching responses:", responseError);
          throw responseError;
        }

        const transformedResponses: JobResponse[] =
          responseData?.map((response) => ({
            id: response.id,
            status: response.status,
            created_at: response.created_at,
            message: response.message,
            jobs: response.jobs
              ? {
                  ...response.jobs,
                  photos: Array.isArray(response.jobs.photos)
                    ? response.jobs.photos.map((photo) => String(photo))
                    : [],
                  details: response.jobs.details || {},
                  owner: response.jobs.owner || null,
                }
              : null,
          })) || [];

        // Sort responses: accepted first, then by date
        const sortedResponses = transformedResponses.sort((a, b) => {
          const aAccepted = a.status === "accepted";
          const bAccepted = b.status === "accepted";

          if (aAccepted !== bAccepted) {
            return aAccepted ? -1 : 1;
          }

          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
        });

        setResponses(sortedResponses);
      } catch (error: any) {
        console.error("Error in fetchResponses:", error);
        toast({
          variant: "destructive",
          title: "Fout bij ophalen reacties",
          description:
            "Er is een fout opgetreden bij het ophalen van je reacties.",
        });
        setResponses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchResponses();
  }, [toast]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-[calc(100vh-85px)] bg-gray-50">
      <div className="container mx-auto px-6 lg:px-8 max-w-7xl py-8">
        <ResponseHeader />
        <div className="mt-8 space-y-8">
          {responses.length === 0 ? (
            <EmptyResponses onViewJobs={() => navigate("/banen")} />
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {responses.map((response) => (
                <ResponseCard key={response.id} response={response} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
