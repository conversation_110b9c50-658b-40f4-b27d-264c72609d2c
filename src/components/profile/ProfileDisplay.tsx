import {
  User,
  Phone,
  Building2,
  Briefcase,
  Receipt,
  Mail,
  Calendar,
  Building,
  Home,
  CheckCircle,
  Pencil,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { ProfileReviews } from "./ProfileReviews";
import { ProfilePortfolio } from "./ProfilePortfolio";
import { Button } from "../ui/button";

interface ProfileDisplayProps {
  profile: any;
  onEdit?: () => void;
}

export const ProfileDisplay = ({ profile, onEdit }: ProfileDisplayProps) => {
  const isVakman = profile?.user_type === "vakman";
  const createdAt = profile?.created_at
    ? new Date(profile.created_at).toLocaleDateString("nl-NL")
    : null;

  const isAddressComplete = profile?.street_address && profile?.house_number;

  return (
    <div className="space-y-6">
      {/* Profile section moved to the top */}
      <Card className="overflow-hidden animate-fade-in">
        <div className="h-32 bg-gradient-primary opacity-90" />
        <div className="relative">
          <div className="absolute -top-16 left-6">
            <Avatar className="h-32 w-32 border-4 border-white shadow-lg">
              <AvatarImage src={profile?.profile_photo_url} />
              <AvatarFallback className="bg-primary">
                <User className="h-16 w-16 text-white" />
              </AvatarFallback>
            </Avatar>
          </div>

          {/* Header with Edit Button */}
          <div className="absolute right-6 -top-12">
            {onEdit && (
              <Button
                onClick={onEdit}
                variant="secondary"
                size="sm"
                className="flex items-center gap-2 bg-white/90 hover:bg-white transition-colors shadow-md"
              >
                <Pencil className="h-4 w-4" />
                Bewerken
              </Button>
            )}
          </div>

          <CardContent className="pt-20">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold text-accent">
                  {profile?.first_name} {profile?.last_name}
                </h2>
                <p className="text-muted-foreground capitalize">
                  {profile?.user_type}
                </p>
                {isVakman && profile?.company_name && (
                  <p className="text-lg font-semibold mt-1 flex items-center gap-2 text-primary">
                    <Building2 className="h-5 w-5" />
                    {profile.company_name}
                  </p>
                )}
                {isVakman && (
                  <div className="space-y-1 mt-2">
                    {profile?.kvk_number && (
                      <p className="text-muted-foreground flex items-center gap-2 hover:text-primary transition-colors">
                        <Briefcase className="h-4 w-4" />
                        KvK: {profile.kvk_number}
                      </p>
                    )}
                    {profile?.btw_number && (
                      <p className="text-muted-foreground flex items-center gap-2 hover:text-primary transition-colors">
                        <Receipt className="h-4 w-4" />
                        BTW: {profile.btw_number}
                      </p>
                    )}
                  </div>
                )}
                {createdAt && (
                  <div className="flex items-center gap-2 text-muted-foreground mt-2">
                    <Calendar className="h-4 w-4" />
                    <span>Lid sinds {createdAt}</span>
                  </div>
                )}
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg text-accent">
                    Contactgegevens
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                      <Mail className="h-4 w-4" />
                      <span>{profile?.email || "Niet ingesteld"}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                      <Phone className="h-4 w-4" />
                      <span>{profile?.phone_number || "Niet ingesteld"}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-lg text-accent">
                      Adresgegevens
                    </h3>
                    {isAddressComplete && (
                      <CheckCircle className="h-5 w-5 text-success-DEFAULT" />
                    )}
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                      <Home className="h-4 w-4" />
                      <span>
                        {profile?.street_address || "Straat niet ingesteld"}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors">
                      <Building className="h-4 w-4" />
                      <span>
                        {profile?.house_number
                          ? `${profile.house_number}${
                              profile.house_number_addition
                                ? ` ${profile.house_number_addition}`
                                : ""
                            }`
                          : "Huisnummer niet ingesteld"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </div>
      </Card>

      {/* Portfolio section for vakman profiles */}
      {isVakman && (
        <div className="space-y-4">
          <ProfilePortfolio userId={profile.id} />
        </div>
      )}

      {/* Reviews section for vakman profiles */}
      {isVakman && <ProfileReviews vakmanId={profile.id} />}
    </div>
  );
};
