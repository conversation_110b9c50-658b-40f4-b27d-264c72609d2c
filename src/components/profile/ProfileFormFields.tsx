import { UseFormReturn } from "react-hook-form";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ProfileFormData } from "@/types/auth";

interface ProfileFormFieldsProps {
  form: UseFormReturn<ProfileFormData>;
  isVakman?: boolean;
  isEditMode?: boolean;
}

export const ProfileFormFields = ({
  form,
  isVakman,
  isEditMode,
}: ProfileFormFieldsProps) => {
  return (
    <>
      <input type="hidden" {...form.register("user_type")} value="vakman" />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="first_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Voornaam</FormLabel>
              <FormControl>
                <Input
                  placeholder="John"
                  onBlurCapture={() => {
                    const capitalizedValue =
                      field.value.charAt(0).toUpperCase() +
                      field.value.slice(1);
                    form.setValue("first_name", capitalizedValue, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="last_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Achternaam</FormLabel>
              <FormControl>
                <Input
                  placeholder="Doe"
                  onBlurCapture={() => {
                    const capitalizedValue =
                      field.value.charAt(0).toUpperCase() +
                      field.value.slice(1);
                    form.setValue("last_name", capitalizedValue, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input type="email" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="city"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Plaats</FormLabel>
            <FormControl>
              <Input placeholder="Amsterdam" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="street_address"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Straatnaam</FormLabel>
            <FormControl>
              <Input placeholder="Hoofdstraat" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="house_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Huisnummer</FormLabel>
              <FormControl>
                <Input placeholder="42" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="house_number_addition"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Toevoeging</FormLabel>
              <FormControl>
                <Input placeholder="A" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="postal_code"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Postcode</FormLabel>
            <FormControl>
              <Input placeholder="1234AB" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="phone_number"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Telefoonnummer</FormLabel>
            <FormControl>
              <Input placeholder="+31 6 12345678" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {isVakman && (
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="company_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Bedrijfsnaam</FormLabel>
                <FormControl>
                  <Input placeholder="Uw Bedrijf B.V." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {isEditMode && (
            <>
              <FormField
                control={form.control}
                name="kvk_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>KvK Nummer</FormLabel>
                    <FormControl>
                      <Input placeholder="12345678" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="btw_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>BTW Nummer</FormLabel>
                    <FormControl>
                      <Input placeholder="NL123456789B01" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
        </div>
      )}
    </>
  );
};
