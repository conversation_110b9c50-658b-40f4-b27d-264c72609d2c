import { Star } from "lucide-react";
import { useEffect, useState } from "react";

import { supabase } from "@/integrations/supabase/client";

interface ProfileReviewsProps {
  vakmanId: string;
}

export const ProfileReviews = ({ vakmanId }: ProfileReviewsProps) => {
  const [reviews, setReviews] = useState<any[]>([]);
  const [averageRating, setAverageRating] = useState<number>(0);

  useEffect(() => {
    const fetchReviews = async () => {
      const { data: reviewsData, error } = await supabase
        .from("vakman_reviews")
        .select(
          `
          *,
          reviewer:profiles!vakman_reviews_reviewer_id_fkey (
            first_name,
            last_name
          ),
          job:jobs (
            title
          )
        `
        )
        .eq("vakman_id", vakmanId);

      if (error) {
        console.error("Error fetching reviews:", error);
      } else if (reviewsData) {
        setReviews(reviewsData);
        const average =
          reviewsData.reduce((acc, review) => acc + review.rating, 0) /
          reviewsData.length;
        setAverageRating(average || 0);
      }
    };

    fetchReviews();
  }, [vakmanId]);

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg text-accent">Beoordelingen</h3>

      {reviews.length > 0 ? (
        <>
          <div className="flex items-center gap-2">
            <span className="text-lg font-semibold">
              {averageRating.toFixed(1)}
            </span>
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`h-5 w-5 ${
                    star <= averageRating
                      ? "text-yellow-400 fill-yellow-400"
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            <span className="text-muted-foreground">
              ({reviews.length}{" "}
              {reviews.length === 1 ? "beoordeling" : "beoordelingen"})
            </span>
          </div>

          <div className="space-y-4 max-h-[400px] overflow-y-auto">
            {reviews.map((review) => (
              <div key={review.id} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${
                            star <= review.rating
                              ? "text-yellow-400 fill-yellow-400"
                              : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-muted-foreground">
                      door {review.reviewer.first_name}{" "}
                      {review.reviewer.last_name}
                    </span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {new Date(review.created_at).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-sm">{review.comment}</p>
                <p className="text-sm text-muted-foreground">
                  Klus: {review.job.title}
                </p>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-8 border rounded-lg bg-muted/10">
          <p className="text-muted-foreground">
            Deze vakman heeft nog geen beoordelingen ontvangen.
          </p>
        </div>
      )}
    </div>
  );
};
