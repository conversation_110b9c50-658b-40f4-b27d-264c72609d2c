import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2 } from "lucide-react";
import { useAuth } from "../auth/hooks/useAuth";

// Define service categories and their items
export const serviceCategories = [
  {
    icon: "🛠️",
    name: "Algemene klussen & bouw",
    services: [
      "<PERSON><PERSON><PERSON><PERSON>",
      "A<PERSON>mer",
      "Timmerman",
      "Metselaar",
      "Stukadoor",
      "Tegelzetter",
      "Betonwerker",
      "<PERSON>hil<PERSON> (binnen/buiten)",
      "Gipsplaatmonteur",
      "Sloopwerker",
    ],
  },
  {
    icon: "💧",
    name: "Installatie & leidingwerk",
    services: [
      "Loodgieter",
      "CV-monteur",
      "Installatiemonteur",
      "Warmtepomp specialist",
      "Zonneboiler installateur",
      "Ventilatiemonteur",
      "Gasinstallateur",
      "Rioolspecialist",
      "Waterontharder installateur",
      "Regenpijp monteur",
    ],
  },
  {
    icon: "⚡",
    name: "Elektra & domotica",
    services: [
      "Elektricien",
      "Zonnepanelen installateur",
      "Domotica specialist",
      "Alarmsysteem installateur",
      "Laadpaal monteur",
      "Netwerkspecialist",
      "Verlichtingsspecialist",
      "Beveiligingsmonteur",
      "Intercom installateur",
      "Brandmeldinstallateur",
    ],
  },
  {
    icon: "🏠",
    name: "Woningrenovatie & interieur",
    services: [
      "Keukenmonteur",
      "Badkamer installateur",
      "Vloerenlegger (laminaat, pvc, parket)",
      "Traprenovatie specialist",
      "Inbouwkast timmerman",
      "Binnenhuisarchitect",
      "Verhuizer",
      "Gashaard specialist",
      "Isolatiespecialist (vloer, muur, dak)",
      "Glazenzetter",
    ],
  },
  {
    icon: "🧰",
    name: "Onderhoud & reparatie",
    services: [
      "Slotenspecialist",
      "Schuurdak reparateur",
      "Dakdekker",
      "Dakgoot specialist",
      "Schoorsteenveger",
      "Dakkapellen specialist",
      "Hang- en sluitwerk monteur",
      "Rolluik- of zonwering monteur",
      "Klusser voor klein onderhoud",
      "Deurensteller / kozijnmonteur",
    ],
  },
  {
    icon: "🌿",
    name: "Tuin & buitenwerk",
    services: [
      "Hovenier",
      "Stratenmaker",
      "Tuinhek monteur",
      "Bestratingsspecialist",
      "Boomverzorger",
      "Grondwerker",
      "Tuinontwerper",
      "Vijverbouwer",
      "Gazonaanlegger",
      "Terrasbouwer",
    ],
  },
  {
    icon: "🧹",
    name: "Schoonmaak & specials",
    services: [
      "Schoonmaker",
      "Gevelreiniger",
      "Opritreiniger",
      "Graffiti-verwijderaar",
      "Vloercoating specialist",
      "Brand- en waterschade hersteller",
      "Ongediertebestrijder",
      "Tapijtreiniger",
      "Ramenwasser",
      "Asbestsaneerder",
    ],
  },
];

const ProfileServices = () => {
  const { userProfile } = useAuth();

  const [selectedServices, setSelectedServices] = useState<string[]>(
    userProfile?.services ?? []
  );
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const handleServiceToggle = (service: string) => {
    setSelectedServices((prev) =>
      prev.includes(service)
        ? prev.filter((s) => s !== service)
        : [...prev, service]
    );
  };

  const handleSaveServices = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          services: selectedServices,
          updated_at: new Date().toISOString(),
        })
        .eq("id", userProfile?.id);
      if (error) throw error;

      toast({
        title: "Diensten opgeslagen",
        description: "Je diensten zijn succesvol opgeslagen.",
      });
    } catch (error) {
      console.error("Error saving services:", error);
      toast({
        variant: "destructive",
        title: "Fout bij opslaan",
        description: "Er ging iets mis bij het opslaan van je diensten.",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-accent">Diensten</h2>
        <Button
          onClick={handleSaveServices}
          disabled={isSaving}
          className="gap-2"
        >
          {isSaving && <Loader2 className="h-4 w-4 animate-spin" />}
          {isSaving ? "Opslaan..." : "Opslaan"}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {serviceCategories.map((category) => (
          <Popover key={category.name}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start gap-2 h-auto py-3"
              >
                <div className="flex flex-col items-start">
                  <span className="font-medium">{category.name}</span>
                  <span className="text-sm text-muted-foreground">
                    {selectedServices.filter((s) =>
                      category.services.includes(s)
                    ).length || "Geen"}{" "}
                    geselecteerd
                  </span>
                </div>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 bg-white">
              <ScrollArea className="pr-4 h-[250px]">
                <div className="space-y-2">
                  {category.services.map((service) => (
                    <div key={service} className="flex items-center space-x-2">
                      <Checkbox
                        id={service}
                        checked={selectedServices.includes(service)}
                        onCheckedChange={() => handleServiceToggle(service)}
                      />
                      <label
                        htmlFor={service}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {service}
                      </label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </PopoverContent>
          </Popover>
        ))}
      </div>

      {selectedServices.length > 0 && (
        <div className="mt-4 p-4 bg-muted rounded-lg">
          <h3 className="font-medium mb-2">Geselecteerde diensten:</h3>
          <div className="flex flex-wrap gap-2">
            {selectedServices.map((service) => (
              <div
                key={service}
                className="bg-background px-2 py-1 rounded-md text-sm flex items-center gap-1"
              >
                {service}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => handleServiceToggle(service)}
                >
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileServices;
