import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { PostgrestError } from "@supabase/supabase-js";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
} from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { generateSecurePassword } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";
import { sendNotificationEmail } from "@/lib/utils";

const generateSampleJobDescription = (jobType: string, message: string) => {
  switch (jobType) {
    case "ventilatie-vervangen-of-reinigen":
      return `Vervangen Ventilatie Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Huidig type: Weet ik niet\n
- Reden vervanging: Gezonder binnenklimaat\n
- Ruimtes: Hele woning\n
- Bestaand systeem: Weet ik niet\n
- Vocht/schimmel: Weet ik niet\n
- Warmteterugwinning: ${message}`;

    case "lekkage-verhelpen":
      return `Lekkage Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Locatie: Weet ik niet\n
- Duur: Net ontdekt\n
- Oorzaak: Onbekend\n
- Schade: Vochtplekken\n
- Ernst: Matig\n
- Eerdere reparatie: Nee`;

    case "huisisolatie":
      return `Isolatie Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type isolatie: Spouwmuurisolatie\n
- Bouwjaar woning: Weet ik niet\n
- Type woning: Tussenwoning\n
- Huidige isolatie: Matig\n
- Hoofdreden: Energiebesparing\n
- Vochtproblemen: Nee`;

    case "traprenovatie":
      return `Traprenovatie Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type trap: Rechte trap\n
- Huidige staat: Matig\n
- Materiaal voorkeur: Hout\n
- Specifieke wensen: Nee\n
- Budget: €1.000 - €3.000\n
- Planning: Binnen 1 maand`;

    case "cv":
      return `CV-ketel Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Probleem: Storing/foutmelding\n
- Leeftijd ketel: 5-10 jaar\n
- Merk: Remeha\n
- Begin probleem: Deze week\n
- Eerder onderhoud: Ja, afgelopen jaar\n
- Storing/foutcode: Onbekend`;

    case "stucwerk-binnen":
      return `Stucwerk Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Oppervlakte: 20-50m²\n
- Type stucwerk: Glad stucwerk\n
- Locatie: Wanden\n
- Ondergrond: Gerenoveerd\n
- Voorbereidend werk: Kleine reparaties\n
- Planning: Binnen 1 maand`;

    case "tuin-bestrating":
      return `Tuinbestrating Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Oppervlakte: 30m²\n
- Type bestrating: Betontegels\n
- Huidige situatie: Bestaande bestrating vervangen\n
- Type ondergrond: Zand\n
- Afwatering: Ja, drainage aanleggen\n
- Bereikbaarheid: Goed`;

    case "waterleiding-vervangen":
      return `Loodgieter Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type werk: Lekkage reparatie\n
- Locatie: Badkamer\n
- Urgentie: Niet acuut\n
- Leeftijd leidingwerk: 10-20 jaar\n
- Eerder werk: Nee, nooit\n
- Bereikbaarheid: Goed`;

    case "dakrenovatie-of-vervanging":
      return `Dak Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type werkzaamheden: Dakreparatie\n
- Type dak: Schuin dak\n
- Dakmateriaal: Dakpannen\n
- Oppervlakte: 50-100m²\n
- Lekkage: Nee\n
- Laatste onderhoud: Weet ik niet`;

    case "dakkapel-plaatsen-vervangen":
      return `Dakkapel Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type project: Nieuwe dakkapel plaatsen\n
- Type dak: Schuin dak\n
- Type raam: Draai-kiepraam\n
- Afmetingen: Middel (2-3 meter)\n
- Materiaal: Kunststof`;

    case "ikea-meubels":
      return `IKEA Meubels Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type meubels: Kast (bijv. PAX, BILLY, BESTÅ)\n
- Aantal meubels: 1 meubel\n
- Leveringsstatus: Ja, alles is al geleverd\n
- Montagelocatie: Begane grond\n
- Specifieke wensen: ${message}`;

    case "aannemer-inschakelen":
      return `Aannemer Project Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type project: Uitbouw of aanbouw\n
- Omvang project: Eén ruimte\n
- Bouwjaar pand: Na 2000\n
- Bouwtekening status: Alleen een schets of moodboard\n
- Vergunning status: Moet nog worden aangevraagd\n
- Werkzaamheden: Fundering / grondwerk\n
- Gewenste startdatum: Binnen 1-3 maanden\n
- Budget indicatie: €25.000 – €50.000\n
- Project omschrijving: ${message}`;

    case "bouwbedrijf-inschakelen":
      return `Bouwbedrijf Project Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type bouwproject: Aanbouw of uitbouw\n
- Gewenste startdatum: Binnen 1 maand\n
- Voorbereidingsstatus: Ik heb nog niets voorbereid\n
- Project omschrijving: ${message}`;

    case "badkamer-renovatie-installatie":
      return `Badkamer Renovatie & Installatie Details:\n
${message}\n
\n
Aanvullende informatie:\n
- Type badkamerwerk: Complete badkamerrenovatie\n
- Oppervlakte badkamer: Gemiddeld (4-8 m²)\n
- Huidige staat: Verouderd, moet volledig vernieuwd\n
- Gewenste voorzieningen: Douche en bad\n
- Budget: €10.000 - €20.000 (complete renovatie)\n
- Gewenste startdatum: Binnen 3 maanden\n
- Specifieke wensen: ${message}`;

    default:
      return `${jobType} Details:\n${message}`;
  }
};

const getJobTitle = (jobType: string): string => {
  switch (jobType) {
    case "ventilatie-vervangen-of-reinigen":
      return "Ventilatie";
    case "lekkage-verhelpen":
      return "Lekkage";
    case "huisisolatie":
      return "Huisisolatie";
    case "traprenovatie":
      return "Traprenovatie";
    case "cv":
      return "CV-ketel";
    case "stucwerk-binnen":
      return "Stucwerk";
    case "tuin-bestrating":
      return "Tuinbestrating";
    case "waterleiding-vervangen":
      return "Loodgieter";
    case "dakrenovatie-of-vervanging":
      return "Dakrenovatie of -Vervanging";
    case "dakkapel-plaatsen-vervangen":
      return "Dakkapel";
    case "ikea-meubels":
      return "IKEA Meubels";
    case "aannemer-inschakelen":
      return "Aannemer";
    case "bouwbedrijf-inschakelen":
      return "Bouwbedrijf";
    case "badkamer-renovatie-installatie":
      return "Badkamer Renovatie";

    default:
      return "Klus";
  }
};

interface ContactUsModalProps {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  jobType: string;
}

const ContactUsModal = ({
  isOpen,
  setIsOpen,
  jobType,
}: ContactUsModalProps) => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    postalCode: "",
    houseNumber: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would handle the form submission
    console.log("Form submitted:", formData);
    const {
      email,
      message,
      firstName,
      phone,
      lastName,
      postalCode,
      houseNumber,
    } = formData;
    const password = generateSecurePassword();
    const jobDescription = generateSampleJobDescription(jobType, message);

    setIsSubmitting(true);
    try {
      // Step 1: User Authentication
      const { data: authData, error: signUpError } = await supabase.auth.signUp(
        {
          email,
          password,
          options: {
            data: { user_type: "klusaanvrager" },
            emailRedirectTo: `${location.origin}/kies-uw-vakman`,
          },
        }
      );

      if (signUpError) {
        if (signUpError.message.includes("exist")) {
          throw new Error(
            "Dit e-mailadres is al in gebruik. Probeer in te loggen."
          );
        }
        throw new Error("Er ging iets mis bij het aanmaken van uw account.");
      }

      if (!authData.user) {
        throw new Error("Er ging iets mis bij het aanmaken van uw account.");
      }

      const userId = authData.user.id;

      // Execute profile update and job creation in parallel
      const [profileResult, jobResult] = await Promise.all([
        // Update Profile
        supabase
          .from("profiles")
          .update({
            full_name: `${firstName} ${lastName}`,
            phone_number: phone,
            first_name: firstName,
            last_name: lastName,
            house_number: houseNumber,
            postal_code: postalCode,
          })
          .eq("id", userId),

        // Create Job
        supabase
          .from("jobs")
          .insert({
            title: getJobTitle(jobType),
            description: jobDescription,
            user_id: userId,
          })
          .select()
          .single(),
      ]);

      if (profileResult.error) {
        throw new Error(
          "Er ging iets mis bij het opslaan van uw profielgegevens."
        );
      }

      if (jobResult.error) {
        const error = jobResult.error as PostgrestError;
        if (error.code === "23503") {
          throw new Error(
            "Er ging iets mis bij het koppelen van de klus aan uw profiel."
          );
        }
        throw new Error("Er ging iets mis bij het aanmaken van de klus.");
      }

      // Success handling
      navigate("/banen", { replace: true });
      toast({
        title: "Registratie succesvol",
        description:
          "Controleer uw e-mailinbox om uw e-mailadres te verifiëren.",
      });

      // Send user information notification
      await sendNotificationEmail({
        to: [email],
        subject: "Welkom bij Klusgebied - Uw accountgegevens",
        content: `
    <div style="font-family: sans-serif; color: #333;">
      <h2>Welkom bij Klusgebied</h2>
      <p>Beste ${firstName} ${lastName},</p>
      <p>Uw account is succesvol aangemaakt. Hieronder vindt u uw inloggegevens:</p>
      <p><strong>Email:</strong> ${email}</p>
      <p><strong>Wachtwoord:</strong> ${password}</p>
      <p style="color: #666; font-size: 14px;">Wij raden u aan dit wachtwoord direct te wijzigen na uw eerste inlog.</p>
      <p><a href="${window.location.origin}/auth" style="color: #0066cc;">Klik hier om in te loggen</a></p>
      <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
      <p style="color: #666; font-size: 12px;">Dit is een automatisch gegenereerd bericht. Bewaar deze email op een veilige plaats.</p>
    </div>
  `,
      });

      // Send user registration notification
      await sendNotificationEmail({
        to: ["<EMAIL>"],
        subject: "Nieuwe gebruiker geregistreerd",
        content: `
          <div style="font-family: sans-serif; color: #333;">
            <h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
            <p><strong>Naam:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Type gebruiker:</strong> klusaanvrager</p>
            <p><a href="${window.location.origin}/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
          </div>
        `,
      });

      // Send new job notification
      await sendNotificationEmail({
        to: ["<EMAIL>"],
        subject: "Nieuwe klusopdracht geplaatst",
        content: `
          <div style="font-family: sans-serif; color: #333;">
            <h2>Nieuwe klusopdracht</h2>
            <p><strong>Titel:</strong> ${getJobTitle(jobType)}</p>
            <p><strong>Klant:</strong> ${firstName} ${lastName}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><a href="${
              window.location.origin
            }/beheerder/berichten" style="color: #0066cc;">Bekijk in admin panel</a></p>
          </div>
        `,
      });
    } catch (error) {
      console.error("Job creation failed:", error);
      toast({
        variant: "destructive",
        title: "Er is iets misgegaan",
        description:
          error instanceof Error
            ? error.message
            : "De klus kon niet worden toegevoegd. Probeer het opnieuw.",
      });
    } finally {
      setIsSubmitting(false);
    }

    // Reset form
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      postalCode: "",
      houseNumber: "",
      message: "",
    });
    setIsOpen(false);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent>
        <DialogHeader>Contact opnemen</DialogHeader>
        <DialogDescription>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              {/* First row - Name fields */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    placeholder="Voornaam*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    placeholder="Achternaam*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Second row - Email and Phone */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder="E-mailadres*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="Telefoonnummer*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Third row - Postal code and House number */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    name="postalCode"
                    value={formData.postalCode}
                    onChange={handleChange}
                    placeholder="Postcode*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex-1">
                  <input
                    type="text"
                    name="houseNumber"
                    value={formData.houseNumber}
                    onChange={handleChange}
                    placeholder="Huisnummer*"
                    required
                    className="w-full p-3 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Message box */}
              <div>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="Je bericht..."
                  rows={4}
                  required
                  className="w-full p-3 border border-gray-300 rounded-md"
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-[#40cfc1] text-white py-3 px-4 rounded-md hover:bg-[#35b5a8] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? "Bezig met verzenden..." : "Contact opnemen"}
              </button>

              <p className="text-xs text-gray-500 mt-4">
                Door je informatie in te dienen, ga je ermee akkoord deze naar
                Klusgebied te sturen, die deze zal verwerken en gebruiken
                volgens hun privacyverklaring.
              </p>
            </div>
          </form>
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
};

export default ContactUsModal;
