import { useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

interface Testimonial {
  id: number;
  name: string;
  rating: number;
  shortText: string;
  fullText: string;
  verified?: boolean;
}

interface TestimonialCarouselProps {
  testimonials: Testimonial[];
  className?: string;
}

const TestimonialCarousel = ({
  testimonials,
  className = "",
}: TestimonialCarouselProps) => {
  const [expandedTestimonials, setExpandedTestimonials] = useState<Set<number>>(
    new Set()
  );

  const toggleExpanded = (id: number) => {
    const newExpanded = new Set(expandedTestimonials);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedTestimonials(newExpanded);
  };

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <svg
        key={i}
        className="w-5 h-5 text-yellow-400"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
      </svg>
    ));
  };

  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
          Wat klanten zeggen
        </h2>
        <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full max-w-7xl mx-auto"
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {testimonials.map((testimonial) => {
              const isExpanded = expandedTestimonials.has(testimonial.id);
              return (
                <CarouselItem
                  key={testimonial.id}
                  className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3 xl:basis-1/4"
                >
                  <div className="bg-white p-6 rounded-lg shadow-md h-full flex flex-col">
                    <div className="flex mb-2">
                      {renderStars(testimonial.rating)}
                    </div>
                    <p className="mb-4 flex-grow text-base tracking-wide leading-relaxed">
                      {isExpanded
                        ? testimonial.fullText
                        : testimonial.shortText}
                    </p>
                    <button
                      onClick={() => toggleExpanded(testimonial.id)}
                      className="text-base text-gray-500 hover:text-[#40cfc1] transition-colors text-left mb-4 tracking-wide leading-relaxed"
                    >
                      {isExpanded ? "Lees minder" : "Lees meer"}
                    </button>
                    <div className="flex items-center">
                      <span className="font-medium text-base tracking-wide leading-relaxed">
                        {testimonial.name}
                      </span>
                      {testimonial.verified && (
                        <svg
                          className="w-4 h-4 ml-1 text-blue-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      )}
                    </div>
                  </div>
                </CarouselItem>
              );
            })}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex" />
          <CarouselNext className="hidden md:flex" />
        </Carousel>
      </div>
    </section>
  );
};

export default TestimonialCarousel;
