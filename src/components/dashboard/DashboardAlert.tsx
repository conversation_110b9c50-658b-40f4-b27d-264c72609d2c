import { AlertCircle } from "lucide-react";
import { useAtom, useAtomValue } from "jotai";
import { useNavigate } from "react-router-dom";
import { useEffect, useState, memo } from "react";

import { Alert } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { isRegisteringAtom } from "../auth/SessionProvider";
import { supabase } from "@/integrations/supabase/client";
import { isProfileCompletedAtom } from "@/states/profile";

interface Profile {
  id: string;
  user_type?: string;
  btw_number?: string;
  kvk_number?: string;
  services?: any[];
  first_name?: string;
  last_name?: string;
  street_address?: string;
  house_number?: string;
  balance?: number;
}

interface DashboardAlertProps {
  profile: Profile;
}

const checkPortfolioProjects = async (userId: string): Promise<boolean> => {
  try {
    const { count } = await supabase
      .from("portfolio_projects")
      .select("*", { count: "exact", head: true })
      .eq("user_id", userId);

    return Boolean(count && count > 0);
  } catch (error) {
    console.error("Error checking portfolio:", error);
    return false;
  }
};

export const DashboardAlert = memo(({ profile }: DashboardAlertProps) => {
  const navigate = useNavigate();
  const isRegistering = useAtomValue(isRegisteringAtom);
  const [isProfileCompleted, setIsProfileCompleted] = useAtom(
    isProfileCompletedAtom
  );

  const [hasPortfolio, setHasPortfolio] = useState(false);
  const [isPortfolioLoading, setIsPortfolioLoading] = useState(false);

  useEffect(() => {
    if (profile?.user_type === "vakman") {
      setIsPortfolioLoading(true);
      checkPortfolioProjects(profile.id)
        .then(setHasPortfolio)
        .finally(() => setIsPortfolioLoading(false));
    }
  }, [profile?.id, profile?.user_type]);

  useEffect(() => {
    setIsProfileCompleted(
      profile &&
        Boolean(
          profile.first_name &&
            profile.last_name &&
            profile.street_address &&
            profile.house_number &&
            profile.kvk_number &&
            profile.btw_number &&
            (profile.user_type !== "vakman" ||
              (profile.services?.length && hasPortfolio))
        )
    );
  }, [profile, hasPortfolio]);

  const getMissingField = (): string | null => {
    if (!profile.btw_number) return "BTW-nummer";
    if (!profile.kvk_number) return "KVK-nummer";
    if (profile.user_type === "vakman") {
      if (!profile.services?.length) return "Diensten";
      if (!hasPortfolio) return "minimaal 1 portfolio project";
    }
    return null;
  };

  if (profile?.user_type === "vakman" && isPortfolioLoading) {
    return null;
  }

  if (profile?.user_type === "vakman" && !profile?.balance) {
    return (
      <Alert
        variant="destructive"
        className="mb-6 rounded-lg border-l-4 border-l-red-600 bg-gradient-to-r from-red-50/90 to-red-50/70 p-4 shadow-lg transition-all duration-200 ease-in-out hover:shadow-xl sm:p-6"
      >
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-6 w-6 shrink-0 animate-pulse text-red-600" />
            <div className="space-y-1">
              <h3 className="font-semibold text-red-700">
                Saldo aanvullen vereist
              </h3>
              <p className="text-sm text-red-600">
                Je saldo is 0 euro. Vul je saldo aan om beschikbare opdrachten
                te kunnen bekijken en gebruik te maken van alle
                functionaliteiten.
              </p>
            </div>
          </div>
          <Button
            variant="default"
            size="sm"
            className="group relative min-w-[140px] bg-red-600 font-medium text-white transition-all duration-200 hover:bg-red-700 hover:shadow-md active:scale-95"
            onClick={() => navigate("/evenwicht")}
          >
            <span className="flex items-center justify-center gap-2">
              Saldo opladen
              <span className="transition-transform group-hover:translate-x-1">
                →
              </span>
            </span>
          </Button>
        </div>
      </Alert>
    );
  }

  if (isProfileCompleted || isRegistering) return null;

  const missingField = getMissingField();

  return (
    <Alert
      variant="destructive"
      className="mb-6 rounded-lg border-l-4 border-l-red-600 bg-gradient-to-r from-red-50/90 to-red-50/70 p-4 shadow-lg transition-all duration-200 ease-in-out hover:shadow-xl sm:p-6"
    >
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-start space-x-3">
          <AlertCircle className="h-6 w-6 shrink-0 animate-pulse text-red-600" />
          <div className="space-y-1">
            <h3 className="font-semibold text-red-700">
              Profiel aanvullen vereist
            </h3>
            <p className="text-sm text-red-600">
              Je profiel is nog niet compleet. Vul je gegevens aan om gebruik te
              maken van alle functionaliteiten.
            </p>
          </div>
        </div>
        <Button
          variant="default"
          size="sm"
          className="group relative min-w-[140px] bg-red-600 font-medium text-white transition-all duration-200 hover:bg-red-700 hover:shadow-md active:scale-95"
          onClick={() => navigate("/profiel")}
        >
          <span className="flex items-center justify-center gap-2">
            Profiel aanvullen
            <span className="transition-transform group-hover:translate-x-1">
              →
            </span>
          </span>
        </Button>
      </div>
    </Alert>
  );
});

DashboardAlert.displayName = "DashboardAlert";
