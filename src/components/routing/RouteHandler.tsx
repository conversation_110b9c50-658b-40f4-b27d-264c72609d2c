import { Routes, Route } from "react-router-dom";
import { getLandingRouteConfig } from "@/config/routes";

// Landing page components
import VentilationLanding from "@/pages/VentilationLanding";
import IkeaLanding from "@/pages/IkeaLanding";
import DakkapelLanding from "@/pages/DakkapelLanding";
import BadkamerLanding from "@/pages/BadkamerLanding";
import LekkageLanding from "@/pages/LekkageLanding";
import IsolatieLanding from "@/pages/IsolatieLanding";
import TraprenovatieLanding from "@/pages/TraprenovatieLanding";
import CVLanding from "@/pages/CVLanding";
import TuinbestratingLanding from "@/pages/TuinbestratingLanding";
import ContractorLanding from "@/pages/ContractorLanding";
import ConstructionLanding from "@/pages/ConstructionLanding";
import RoofingLanding from "@/pages/RoofingLanding";
import WaterleidingLanding from "@/pages/WaterleidingLanding";

// Component mapping for landing pages
const LANDING_COMPONENTS = {
  VentilationLanding,
  IkeaLanding,
  DakkapelLanding,
  BadkamerLanding,
  LekkageLanding,
  IsolatieLanding,
  TraprenovatieLanding,
  CVLanding,
  TuinbestratingLanding,
  ContractorLanding,
  ConstructionLanding,
  RoofingLanding,
  WaterleidingLanding,
} as const;

interface LandingRouteHandlerProps {
  pathname: string;
}

export const LandingRouteHandler = ({ pathname }: LandingRouteHandlerProps) => {
  const config = getLandingRouteConfig(pathname);

  if (!config) {
    return null;
  }

  const Component =
    LANDING_COMPONENTS[config.component as keyof typeof LANDING_COMPONENTS];

  if (!Component) {
    console.error(
      `Component ${config.component} not found for route ${pathname}`
    );
    return null;
  }

  return (
    <Routes>
      <Route path={config.path} element={<Component />} />
    </Routes>
  );
};

// Public routes handler
import Auth from "@/pages/Auth";
import WerkwijzePage from "@/pages/WerkwijzePage";
import CompanyFormPage from "@/pages/CompanyFormPage";
import WaaromPage from "@/pages/WaaromPage";
import ResetPasswordPage from "@/pages/ResetPasswordPage";
import { UnAuthorizedNewJobForm } from "@/components/werkwijze/new-jobs/UnAuthorizedNewJobForm";
import { ROUTE_PATHS } from "@/config/routes";

export const PublicRouteHandler = () => {
  return (
    <Routes>
      <Route path={ROUTE_PATHS.AUTH} element={<Auth />} />
      <Route path={ROUTE_PATHS.PLACE_JOB} element={<WerkwijzePage />} />
      <Route path={ROUTE_PATHS.FIND_PERSONNEL} element={<CompanyFormPage />} />
      <Route path="/plaats-een-klus/:id" element={<UnAuthorizedNewJobForm />} />
      <Route path="/weblogs" element={<Blogs />} />
      <Route path="/weblogs/:slug" element={<BlogDetails/>} />
      <Route path={ROUTE_PATHS.LOGIN} element={<WaaromPage />} />
      <Route
        path={ROUTE_PATHS.RESET_PASSWORD}
        element={<ResetPasswordPage />}
      />
      <Route path={ROUTE_PATHS.PRIVACY} element={<Privacy />} />
      <Route path={ROUTE_PATHS.TERMS} element={<Terms />} />
    </Routes>
  );
};

// Protected routes handler
import { Navigate } from "react-router-dom";
import Index from "@/pages/Index";
import Balance from "@/pages/Balance";
import Reviews from "@/pages/Reviews";
import BonusSystem from "@/pages/BonusSystem";
import { ProfileSection } from "@/components/ProfileSection";
import { JobList } from "@/components/JobList";
import { MyResponses } from "@/components/MyResponses";
import { VakmanList } from "@/components/VakmanList";
import { JobDetail } from "@/components/JobDetail";
import { CreateJobForm } from "@/components/CreateJobForm";
import { PortfolioPage } from "@/components/portfolio/PortfolioPage";
import ChatPage from "@/pages/ChatPage";
import { GeneralNewJobForm } from "@/components/werkwijze/new-jobs/GeneralNewJobForm";
import { NewMFA } from "@/components/security/NewMFA";

// Admin components
import AdminDashboard from "@/pages/AdminDashboard";
import DatabasePage from "@/pages/admin/DatabasePage";
import AnalyticsPage from "@/pages/admin/AnalyticsPage";
import ReportsPage from "@/pages/admin/ReportsPage";
import MessagesPage from "@/pages/admin/MessagesPage";
import { CompanyFormsList } from "@/pages/admin/CompanyFormListPage";
import CraftsmenMapView from "@/pages/admin/CraftsmenMapView";
import CraftmanUsersPage from "@/pages/admin/CraftmanUsersPage";
import KlasaanvragerUsersPage from "@/pages/admin/KlusaanvragerUsersPage";

import JobTemplatePage from "@/pages/admin/JobTemplatePage";
import Blogs from "../webblogs/Blogs";
import Privacy from "@/pages/privacy";
import Terms from "@/pages/terms";
import { BlogDetails } from "../webblogs/BlogDetails";

export const ProtectedRouteHandler = () => {
  return (
    <Routes>
      <Route path={ROUTE_PATHS.HOME} element={<Index />} />
      <Route path={ROUTE_PATHS.BALANCE} element={<Balance />} />
      <Route path={ROUTE_PATHS.PROFILE} element={<ProfileSection />} />
      <Route path={ROUTE_PATHS.PROFILE_MFA} element={<NewMFA />} />
      <Route path={ROUTE_PATHS.PORTFOLIO} element={<PortfolioPage />} />
      <Route path={ROUTE_PATHS.JOBS} element={<JobList />} />
      <Route path={ROUTE_PATHS.JOBS_NEW} element={<CreateJobForm />} />
      <Route path="/banen/new/:id" element={<GeneralNewJobForm />} />
      <Route path={ROUTE_PATHS.JOBS_DETAIL} element={<JobDetail />} />
      <Route path={ROUTE_PATHS.MY_RESPONSES} element={<MyResponses />} />
      <Route path={ROUTE_PATHS.CHOOSE_CRAFTSMAN} element={<VakmanList />} />
      <Route path={ROUTE_PATHS.REVIEWS} element={<Reviews />} />
      <Route path={ROUTE_PATHS.CONVERSATIONS} element={<ChatPage />} />
      <Route path={ROUTE_PATHS.BONUS} element={<BonusSystem />} />

      {/* Admin Routes */}
      <Route path={ROUTE_PATHS.ADMIN} element={<AdminDashboard />} />
      <Route path={ROUTE_PATHS.ADMIN_DATABASE} element={<DatabasePage />} />
      <Route path={ROUTE_PATHS.ADMIN_ANALYTICS} element={<AnalyticsPage />} />
      <Route path={ROUTE_PATHS.ADMIN_REPORTS} element={<ReportsPage />} />
      <Route path={ROUTE_PATHS.ADMIN_MESSAGES} element={<MessagesPage />} />
      <Route
        path={ROUTE_PATHS.ADMIN_COMPANY_REQUESTS}
        element={<CompanyFormsList />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_CRAFTSMEN_MAP}
        element={<CraftsmenMapView />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_CRAFTSMEN}
        element={<CraftmanUsersPage />}
      />
      <Route
        path={ROUTE_PATHS.ADMIN_CUSTOMERS}
        element={<KlasaanvragerUsersPage />}
      />

      <Route
        path={ROUTE_PATHS.ADMIN_JOB_TEMPLATES}
        element={<JobTemplatePage />}
      />

      <Route path="*" element={<Navigate to={ROUTE_PATHS.HOME} replace />} />
    </Routes>
  );
};
