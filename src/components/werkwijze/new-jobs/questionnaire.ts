import {
  ArrowUpDown,
  Building,
  Building2,
  Droplet,
  Flame,
  Hammer,
  HardHat,
  Home,
  PaintBucket,
  Search,
  Shovel,
  ShowerHead,
  Wind,
} from "lucide-react";

import { Questionnaire, QuestionnaireItem } from "./types";

const leakRepair: Questionnaire[] = [
  {
    id: 1,
    question: "Waar bevindt zich de lekkage?",
    options: [
      "<PERSON><PERSON>",
      "<PERSON><PERSON>",
      "<PERSON>la<PERSON>nd",
      "<PERSON><PERSON><PERSON>",
      "Bad<PERSON><PERSON>",
      "<PERSON><PERSON><PERSON>",
      "<PERSON>",
    ],
  },
  {
    id: 2,
    question: "Hoe lang bestaat de lekkage al?",
    options: [
      "Net ontdekt",
      "Enkele dagen",
      "Een week",
      "<PERSON>r dan een maand",
    ],
  },
  {
    id: 3,
    question: "Is de oorzaak bekend?",
    options: [
      "Ja, kapotte leiding",
      "Ja, door regen",
      "Ja, anders",
      "Nee, onbekend",
    ],
  },
  {
    id: 4,
    question: "Is er zichtbare schade?",
    options: [
      "Ja, vocht<PERSON>k<PERSON>",
      "<PERSON><PERSON>, schimme<PERSON>",
      "<PERSON><PERSON>, be<PERSON>",
      "<PERSON>ee, alleen lekkage",
    ],
  },
  {
    id: 5,
    question: "Hoe ernstig is de lekkage?",
    options: [
      "He<PERSON> ernstig (constant)",
      "Matig (druppels)",
      "Licht (vochtig)",
      "Wisselend",
    ],
  },
];

const houseInsulation: Questionnaire[] = [
  {
    id: 1,
    question: "Welk type isolatie wil je laten uitvoeren?",
    options: [
      "Spouwmuurisolatie",
      "Dakisolatie",
      "Vloerisolatie",
      "Glasisolatie",
      "Complete woningisolatie",
    ],
  },
  {
    id: 2,
    question: "Wat is het bouwjaar van je woning?",
    options: ["Voor 1920", "1920-1975", "1975-1990", "1990-2005", "Na 2005"],
  },
  {
    id: 3,
    question: "Wat is het type woning?",
    options: [
      "Vrijstaand huis",
      "Hoekwoning",
      "Tussenwoning",
      "Appartement",
      "Anders",
    ],
  },
  {
    id: 4,
    question: "Wat is de huidige isolatiewaarde van je woning?",
    options: [
      "Geen isolatie",
      "Matige isolatie",
      "Redelijke isolatie",
      "Weet ik niet",
    ],
  },
  {
    id: 5,
    question: "Wat is je belangrijkste reden voor isolatie?",
    options: [
      "Energiebesparing",
      "Comfort verbetering",
      "Geluidsreductie",
      "Verduurzaming",
      "Waardeverhoging",
    ],
  },
];

const stairRenovation: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type trap wilt u?",
    options: ["Rechtrapp", "L-vormige trap", "U-vormige trap", "Spiraaltrap"],
  },
  {
    id: 2,
    question: "Wat is de huidige staat van de trap?",
    options: ["Goed", "Matig", "Slecht", "Onbekend"],
  },
  {
    id: 3,
    question: "Wat voor materiaal heeft uw voorkeur?",
    options: ["Hout", "Beton", "Metaal", "Combinatie"],
  },
  {
    id: 4,
    question: "Heeft u specifieke wensen voor de trap?",
    options: ["Ja", "Nee"],
  },
  {
    id: 5,
    question: "Wat is uw budget voor de traprenovatie?",
    options: ["< €1.000", "€1.000 - €3.000", "€3.000 - €5.000", "> €5.000"],
  },
];

const ventilationReplacement: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type ventilatie heeft u momenteel?",
    options: [
      "Natuurlijke ventilatie",
      "Mechanische ventilatie",
      "Balansventilatie",
      "Weet ik niet",
    ],
  },
  {
    id: 2,
    question: "Wat is de reden voor vervanging?",
    options: [
      "Defect/storing",
      "Verouderd systeem",
      "Energiebesparing",
      "Gezonder binnenklimaat",
    ],
  },
  {
    id: 3,
    question: "In welke ruimtes heeft u ventilatie nodig?",
    options: [
      "Alleen badkamer",
      "Keuken en badkamer",
      "Hele woning",
      "Specifieke ruimtes",
    ],
  },
  {
    id: 4,
    question: "Is er al een ventilatiesysteem aanwezig?",
    options: [
      "Ja, moet vervangen worden",
      "Ja, moet gerepareerd worden",
      "Nee, nieuwe installatie",
      "Weet ik niet",
    ],
  },
  {
    id: 5,
    question: "Heeft u last van vocht of schimmel?",
    options: [
      "Ja, veel last",
      "Ja, beetje last",
      "Nee, geen last",
      "Weet ik niet",
    ],
  },
];

const cvRepair: Questionnaire[] = [
  {
    id: 1,
    question: "Wat is het probleem met je CV-ketel?",
    options: [
      "Geen warm water",
      "Geen verwarming",
      "Vreemde geluiden",
      "Lekkage",
      "Storing/foutmelding",
      "Periodiek onderhoud",
    ],
  },
  {
    id: 2,
    question: "Hoe oud is je CV-ketel?",
    options: [
      "0-5 jaar",
      "5-10 jaar",
      "10-15 jaar",
      "15+ jaar",
      "Weet ik niet",
    ],
  },
  {
    id: 3,
    question: "Welk merk CV-ketel heb je?",
    options: [
      "Remeha",
      "Nefit",
      "Intergas",
      "ATAG",
      "Vaillant",
      "AWB",
      "Anders/weet niet",
    ],
  },
  {
    id: 4,
    question: "Wanneer is het probleem begonnen?",
    options: [
      "Vandaag",
      "Deze week",
      "Deze maand",
      "Al langer",
      "Komt en gaat",
    ],
  },
  {
    id: 5,
    question: "Is er al eerder onderhoud uitgevoerd?",
    options: [
      "Ja, afgelopen jaar",
      "Ja, 1-2 jaar geleden",
      "Ja, langer geleden",
      "Nee, nog nooit",
      "Weet ik niet",
    ],
  },
];

const plasterworkInside: Questionnaire[] = [
  {
    id: 1,
    question: "Wat is de oppervlakte van het te stucen gebied (in m²)?",
    options: ["0-20", "20-50", "50-100", "100+"],
  },
  {
    id: 2,
    question: "Welk type stucwerk wilt u?",
    options: [
      "Glad stucwerk",
      "Spachtelputz",
      "Sierpleister",
      "Spackspuiten",
      "Anders",
    ],
  },
  {
    id: 3,
    question: "Waar moet het stucwerk worden aangebracht?",
    options: ["Wanden", "Plafond", "Beide", "Anders"],
  },
  {
    id: 4,
    question: "Wat is de huidige staat van de ondergrond?",
    options: [
      "Nieuwbouw",
      "Gerenoveerd",
      "Beschadigd",
      "Moet verwijderd worden",
    ],
  },
  {
    id: 5,
    question: "Is er voorbereidend werk nodig?",
    options: [
      "Nee, ondergrond is klaar",
      "Ja, kleine reparaties",
      "Ja, grote reparaties",
      "Weet ik niet",
    ],
  },
];

const gardenPaving: Questionnaire[] = [
  {
    id: 1,
    question: "Wat is de oppervlakte van het te bestraten gebied (in m²)?",
    options: ["Bijv. 30", "Bijv. 50", "Bijv. 100"],
  },
  {
    id: 2,
    question: "Welk type bestrating wil je laten plaatsen?",
    options: ["Betontegels", "Gebakken klinkers", "Natuursteen", "Anders"],
  },
  {
    id: 3,
    question: "Wat is de huidige situatie?",
    options: [
      "Nieuwe tuin (nog geen bestrating)",
      "Bestaande bestrating vervangen",
      "Uitbreiding van bestaande bestrating",
    ],
  },
  {
    id: 4,
    question: "Wat voor type ondergrond is er aanwezig?",
    options: ["Zand", "Klei", "Onbekend"],
  },
  {
    id: 5,
    question: "Is er afwatering nodig?",
    options: [
      "Ja, drainage aanleggen",
      "Ja, aansluiten op bestaande afvoer",
      "Nee, niet nodig",
    ],
  },
];

const waterPipeReplacement: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type loodgieterswerk heeft u nodig?",
    options: [
      "Lekkage reparatie",
      "Nieuwe installatie",
      "Onderhoud",
      "Vervanging",
      "Anders",
    ],
  },
  {
    id: 2,
    question: "Waar bevindt zich het probleem?",
    options: [
      "Badkamer",
      "Keuken",
      "Toilet",
      "Buiten",
      "Anders/meerdere locaties",
    ],
  },
  {
    id: 3,
    question: "Is er sprake van een acute situatie?",
    options: [
      "Ja, er is een lekkage",
      "Ja, geen water",
      "Nee, niet acuut",
      "Anders",
    ],
  },
  {
    id: 4,
    question: "Wat is de leeftijd van uw leidingwerk?",
    options: [
      "0-10 jaar",
      "10-20 jaar",
      "20-30 jaar",
      "30+ jaar",
      "Weet ik niet",
    ],
  },
  {
    id: 5,
    question: "Is er eerder werk aan verricht?",
    options: ["Ja, recent", "Ja, langer geleden", "Nee, nooit", "Weet ik niet"],
  },
];

const ikeaMeubels: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor soort Ikea meubels wil je laten monteren?",
    options: [
      "Kast (bijv. PAX, BILLY, BESTÅ)",
      "Bed (bijv. MALM, HEMNES, SLÄKT)",
      "Bank of zitmeubel",
      "Kindermeubels",
      "Meerdere meubels (geef details bij vraag 5)",
      "Anders, namelijk…",
    ],
  },
  {
    id: 2,
    question: "Hoeveel meubels wil je laten monteren?",
    options: [
      "1 meubel",
      "2–3 meubels",
      "4 of meer meubels",
      "Weet ik nog niet precies",
    ],
  },
  {
    id: 3,
    question: "Zijn de meubels al op locatie aanwezig?",
    options: [
      "Ja, alles is al geleverd",
      "De meubels worden nog geleverd",
      "Gedeeltelijk (sommige al binnen)",
      "Nee, nog niets ontvangen",
    ],
  },
  {
    id: 4,
    question: "Waar moeten de meubels worden gemonteerd?",
    options: [
      "Begane grond",
      "Verdieping (trap aanwezig)",
      "Meerdere ruimtes",
      "Anders, namelijk…",
    ],
  },
  {
    id: 5,
    question:
      "Kun je in het kort omschrijven om welke meubels het gaat en wat er belangrijk is voor jou?",
    type: "textarea",
    placeholder:
      "Wij hebben een PAX kast met schuifdeuren, een MALM bed en twee BILLY kasten. Alles is al geleverd en staat op zolder. Er zijn schroefjes bijgeleverd. Wij willen graag dat het stevig wordt gemonteerd en waterpas staat.",
  },
];

const hireContractor: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor soort project wil je laten uitvoeren?",
    options: [
      "Uitbouw of aanbouw",
      "Verbouwing van badkamer, keuken of zolder",
      "Complete woningrenovatie",
      "Nieuwbouw of casco-opbouw",
      "Garage of bijgebouw realiseren",
      "Anders, namelijk...",
    ],
  },
  {
    id: 2,
    question: "Wat is de omvang van het project?",
    options: [
      "Eén ruimte",
      "Meerdere ruimtes (bijvoorbeeld keuken én woonkamer)",
      "Volledige verdieping of woning",
      "Buitenwerk of fundering",
      "Nog onbekend, ik wil eerst advies",
    ],
  },
  {
    id: 3,
    question: "Wat is het bouwjaar van de woning of pand?",
    options: [
      "Voor 1945",
      "1945–1975",
      "1975–2000",
      "Na 2000",
      "Weet ik niet precies",
    ],
  },
  {
    id: 4,
    question: "Is er een bestaande bouwtekening of plan?",
    options: [
      "Ja, inclusief werktekeningen",
      "Alleen een schets of moodboard",
      "Nee, dit moet nog uitgewerkt worden",
      "Niet van toepassing",
    ],
  },
  {
    id: 5,
    question: "Heb je een vergunning nodig of al geregeld?",
    options: [
      "Ja, vergunning is rond",
      "In aanvraag",
      "Moet nog worden aangevraagd",
      "Geen vergunning nodig (bijvoorbeeld kleine verbouwing)",
      "Weet ik niet – graag advies",
    ],
  },
  {
    id: 6,
    question:
      "Welke onderdelen verwacht je dat de aannemer uitvoert of regelt?",
    options: [
      "Fundering / grondwerk",
      "Metselwerk / ruwbouw",
      "Dakwerk / dakopbouw",
      "Isolatie (vloer, wand, dak)",
      "Elektrawerk",
      "Loodgieterswerk (cv, water, afvoer)",
      "Tegelwerk / stucwerk",
      "Binnenafwerking (vloeren, schilderwerk)",
      "Timmerwerk (kasten, trappen, aftimmering)",
      "Coördinatie onderaannemers",
      "Vergunning- of architectbegeleiding",
      "Anders, namelijk…",
    ],
  },
  {
    id: 7,
    question: "Wat is je gewenste startmoment?",
    options: [
      "Binnen 1 maand",
      "Binnen 1–3 maanden",
      "Over 3–6 maanden",
      "Later dan 6 maanden",
      "Geen specifieke voorkeur",
    ],
  },
  {
    id: 8,
    question: "Wat is je beoogde budget (indicatief)?",
    options: [
      "< €10.000",
      "€10.000 – €25.000",
      "€25.000 – €50.000",
      "€50.000 – €100.000",
      " > €100.000",
      "Weet ik nog niet / liever eerst advies",
    ],
  },
  {
    id: 9,
    question: "Kun je kort omschrijven wat er moet gebeuren?",
    type: "textarea",
    placeholder:
      "Uitbouw van 2,5m met openslaande deuren, inclusief vloerverwarming, elektra en keukenvoorbereiding. Fundering moet worden gestort. Vergunning is nog niet geregeld.",
  },
];

const constructionCompany: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type bouwproject betreft het?",
    options: [
      "Aanbouw of uitbouw",
      "Complete woningrenovatie",
      "Nieuwbouw (casco of volledig)",
      "Verbouwing van een specifieke ruimte (bijv. badkamer, zolder, keuken)",
      "Anders",
    ],
  },
  {
    id: 2,
    question: "Wanneer wil je starten met het project?",
    options: [
      "Zo snel mogelijk",
      "Binnen 1 maand",
      "Binnen 3 maanden",
      "Langer dan 3 maanden",
      "Geen specifieke voorkeur",
    ],
  },
  {
    id: 3,
    question: "Hoe ver is het project voorbereid?",
    options: [
      "Ik heb al tekeningen én vergunning",
      "Ik heb alleen tekeningen of een globaal idee",
      "Ik heb nog niets voorbereid",
      "Niet van toepassing",
    ],
  },
  {
    id: 4,
    question: "Kun je in het kort omschrijven wat er moet gebeuren?",
    type: "textarea",
    placeholder:
      "Ik wil een uitbouw van 3 meter aan de achterzijde van mijn woning, inclusief nieuwe keuken en openslaande deuren. De vergunning is nog niet geregeld.",
  },
];

const roofRenovation: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type dakwerkzaamheden heeft u nodig?",
    options: [
      "Dakreparatie",
      "Dakvervanging",
      "Dakisolatie",
      "Dakgoot reparatie/vervanging",
      "Inspectie/onderhoud",
    ],
  },
  {
    id: 2,
    question: "Wat voor type dak heeft u?",
    options: [
      "Plat dak",
      "Schuin dak",
      "Zadeldak",
      "Lessenaarsdak",
      "Weet ik niet",
    ],
  },
  {
    id: 3,
    question: "Wat is het huidige dakmateriaal?",
    options: ["Dakpannen", "EPDM", "Bitumen", "Riet", "Anders/weet niet"],
  },
  {
    id: 4,
    question: "Wat is de geschatte oppervlakte van het dak?",
    options: ["< 50m²", "50-100m²", "100-150m²", "> 150m²", "Weet ik niet"],
  },
  {
    id: 5,
    question: "Is er sprake van lekkage?",
    options: [
      "Ja, actieve lekkage",
      "Ja, vochtplekken zichtbaar",
      "Nee",
      "Weet ik niet",
    ],
  },
];

const bathroomRenovation: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type badkamerwerk heeft u nodig?",
    options: [
      "Complete badkamerrenovatie",
      "Nieuwe badkamer installeren",
      "Inloopdouche plaatsen",
      "Bad vervangen of installeren",
      "Tegelwerk en afwerking",
      "Leidingwerk en elektra",
      "Onderhoud en reparatie",
    ],
  },
  {
    id: 2,
    question: "Wat is de oppervlakte van de badkamer (in m²)?",
    options: [
      "Klein (tot 4 m²)",
      "Gemiddeld (4-8 m²)",
      "Groot (8-12 m²)",
      "Zeer groot (12+ m²)",
      "Weet ik niet precies",
    ],
  },
  {
    id: 3,
    question: "Wat is de huidige staat van uw badkamer?",
    options: [
      "Verouderd, moet volledig vernieuwd",
      "Functioneel, maar wil moderniseren",
      "Gedeeltelijk defect",
      "Nieuwbouw, nog geen badkamer",
      "Goed, alleen kleine aanpassingen",
    ],
  },
  {
    id: 4,
    question: "Welke voorzieningen wilt u in de badkamer?",
    options: [
      "Douche en bad",
      "Alleen inloopdouche",
      "Alleen bad",
      "Douche, bad en tweede toilet",
      "Weet ik nog niet",
    ],
  },
  {
    id: 5,
    question: "Wat is uw budget voor de badkamerrenovatie?",
    options: [
      "Tot €5.000 (kleine renovatie)",
      "€5.000 - €10.000 (gemiddelde renovatie)",
      "€10.000 - €20.000 (complete renovatie)",
      "€20.000+ (luxe badkamer)",
      "Weet ik nog niet",
    ],
  },
  {
    id: 6,
    question: "Wanneer wilt u starten met de renovatie?",
    options: [
      "Zo snel mogelijk",
      "Binnen 1 maand",
      "Binnen 3 maanden",
      "Later dit jaar",
      "Nog geen specifieke planning",
    ],
  },
];

const dormer: Questionnaire[] = [
  {
    id: 1,
    question: "Wat voor type project wilt u uitvoeren?",
    options: [
      "Nieuwe dakkapel plaatsen",
      "Bestaande dakkapel vervangen",
      "Dakkapel renoveren",
      "Weet ik nog niet",
    ],
  },
  {
    id: 2,
    question: "Wat voor type dak heeft u?",
    options: ["Schuin dak", "Plat dak", "Mansardedak", "Anders"],
  },
  {
    id: 3,
    question: "Welk type raam heeft uw voorkeur?",
    options: ["Vast raam", "Draai-kiepraam", "Schuifraam", "Nog geen voorkeur"],
  },
  {
    id: 4,
    question: "Wat zijn de gewenste afmetingen?",
    options: [
      "Klein (tot 2 meter)",
      "Middel (2-3 meter)",
      "Groot (3+ meter)",
      "Nog niet bekend",
    ],
  },
  {
    id: 5,
    question: "Welk materiaal heeft uw voorkeur?",
    options: ["Kunststof", "Hout", "Aluminium", "Combinatie", "Geen voorkeur"],
  },
];

export const questionnaire: QuestionnaireItem[] = [
  {
    id: "lekkage-verhelpen",
    label: "Lekkage Verhelpen",
    questions: leakRepair,
    icon: Search,
    services: [
      "Loodgieter",
      "Dakdekker",
      "Klusjesman",
      "Rioolspecialist",
      "Brand- en waterschade hersteller",
    ],
  },
  {
    id: "huisisolatie",
    label: "Huisisolatie",
    questions: houseInsulation,
    icon: Home,
    services: [
      "Isolatiespecialist (vloer, muur, dak)",
      "Aannemer",
      "Stukadoor",
      "Klusjesman",
    ],
  },
  {
    id: "traprenovatie",
    label: "Traprenovatie",
    questions: stairRenovation,
    icon: ArrowUpDown,
    services: [
      "Traprenovatie specialist",
      "Timmerman",
      "Aannemer",
      "Klusjesman",
    ],
  },
  {
    id: "ventilatie-vervangen-of-reinigen",
    label: "Ventilatie Vervangen",
    questions: ventilationReplacement,
    icon: Wind,
    services: [
      "Ventilatiemonteur",
      "Installatiemonteur",
      "Elektricien",
      "Klusjesman",
    ],
  },
  {
    id: "cv",
    label: "CV Repareren of Onderhouden",
    questions: cvRepair,
    icon: Flame,
    services: [
      "CV-monteur",
      "Loodgieter",
      "Installatiemonteur",
      "Gasinstallateur",
      "Warmtepomp specialist",
    ],
  },
  {
    id: "stucwerk-binnen",
    label: "Stucwerk Binnen",
    questions: plasterworkInside,
    icon: PaintBucket,
    services: [
      "Stukadoor",
      "Schilder (binnen/buiten)",
      "Gipsplaatmonteur",
      "Klusjesman",
    ],
  },
  {
    id: "tuin-bestrating",
    label: "Tuin Bestrating",
    questions: gardenPaving,
    icon: Shovel,
    services: [
      "Stratenmaker",
      "Bestratingsspecialist",
      "Hovenier",
      "Grondwerker",
      "Terrasbouwer",
    ],
  },
  {
    id: "waterleiding-vervangen",
    label: "Waterleiding Vervangen",
    questions: waterPipeReplacement,
    icon: Droplet,
    services: [
      "Loodgieter",
      "Installatiemonteur",
      "Rioolspecialist",
      "Klusjesman",
    ],
  },
  {
    id: "badkamer-renovatie-installatie",
    label: "Badkamer Renovatie & Installatie",
    questions: bathroomRenovation,
    icon: ShowerHead,
    services: [
      "Badkamer installateur",
      "Loodgieter",
      "Tegelzetter",
      "Elektricien",
      "Timmerman",
      "Stukadoor",
      "Aannemer",
    ],
  },
  {
    id: "dakrenovatie-of-vervanging",
    label: "Dakrenovatie of -Vervanging",
    questions: roofRenovation,
    icon: Home,
    services: [
      "Dakdekker",
      "Dakgoot specialist",
      "Isolatiespecialist (vloer, muur, dak)",
      "Aannemer",
      "Klusjesman",
    ],
  },
  {
    id: "dakkapel-plaatsen-vervangen",
    label: "Dakkapel Plaatsen of Vervangen",
    questions: dormer,
    icon: Building,
    services: [
      "Dakkapellen specialist",
      "Dakdekker",
      "Timmerman",
      "Glazenzetter",
      "Aannemer",
    ],
  },
  {
    id: "ikea-meubels",
    label: "IKEA Meubels",
    questions: ikeaMeubels,
    icon: Hammer,
    hasCustomQuestion: true,
    services: ["Klusjesman", "Timmerman", "Inbouwkast timmerman"],
  },
  {
    id: "aannemer-inschakelen",
    label: "Aannemer Inschakelen",
    questions: hireContractor,
    icon: HardHat,
    hasCustomQuestion: true,
    services: [
      "Aannemer",
      "Timmerman",
      "Metselaar",
      "Elektricien",
      "Loodgieter",
      "Stukadoor",
      "Tegelzetter",
      "Schilder (binnen/buiten)",
      "Isolatiespecialist (vloer, muur, dak)",
    ],
  },
  {
    id: "bouwbedrijf-inschakelen",
    label: "Bouwbedrijf Inschakelen",
    questions: constructionCompany,
    icon: Building2,
    hasCustomQuestion: true,
    services: [
      "Aannemer",
      "Metselaar",
      "Betonwerker",
      "Timmerman",
      "Dakdekker",
      "Elektricien",
      "Loodgieter",
      "Stukadoor",
      "Tegelzetter",
      "Schilder (binnen/buiten)",
      "Isolatiespecialist (vloer, muur, dak)",
      "Grondwerker",
    ],
  },
];
