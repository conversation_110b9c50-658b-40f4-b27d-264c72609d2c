import { XCircle } from "lucide-react";
import { Dispatch, SetStateAction, useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";

export function FileUploadButton({
  fileList,
  setFileList,
}: {
  fileList: File[];
  setFileList: Dispatch<SetStateAction<File[]>>;
}) {
  const [previewUrls, setPreviewUrls] = useState<string[]>(() =>
    fileList.map((item) => URL.createObjectURL(item))
  );
  const [showUpload, setShowUpload] = useState<string>();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    const totalFiles = fileList.length + files.length;

    if (totalFiles > 4) {
      toast({
        variant: "destructive",
        title: "Te veel foto's",
        description: "Je kunt maximaal 4 foto's uploaden",
      });
      e.target.value = "";
      return;
    }

    const newFiles = Array.from(files);
    const allFiles = [...fileList, ...newFiles];
    setFileList(allFiles);

    const newPreviewUrls = newFiles.map((file) => URL.createObjectURL(file));
    setPreviewUrls((prev) => [...prev, ...newPreviewUrls]);
  };

  const handleRemovePhoto = (index: number) => {
    const updatedFiles = fileList.filter((_, i) => i !== index);
    setFileList(updatedFiles);

    URL.revokeObjectURL(previewUrls[index]);
    setPreviewUrls((prev) => prev.filter((_, i) => i !== index));
  };

  if (!showUpload) {
    return (
      <div className="space-y-4 mb-24">
        <Label>Wil je een foto uploaden?</Label>
        <div className="space-x-4">
          <Button variant="outline" onClick={() => setShowUpload("no")}>
            Nee
          </Button>
          <Button className="text-white" onClick={() => setShowUpload("yes")}>
            Ja
          </Button>
        </div>
      </div>
    );
  } else if (showUpload === "no") {
    return (
      <div className="mb-24">
        <Label>Je kunt naar de volgende stap gaan</Label>
      </div>
    );
  }

  return (
    <div className="space-y-2 min-h-48">
      <Label>Upload foto's (maximaal 4)</Label>
      <Input
        type="file"
        accept="image/*"
        multiple
        onChange={handleFileChange}
        className="bg-white border-gray-200 focus:border-primary cursor-pointer flex items-center justify-center file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-white file:text-black hover:file:bg-gray-100 file:transition-colors text-center"
      />

      {previewUrls.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm">Nieuwe foto's:</p>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {previewUrls.map((url, index) => (
              <div
                key={`preview-${index}`}
                className="relative rounded-lg overflow-hidden border border-gray-200 group bg-gray-50"
              >
                <img
                  src={url}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-0 w-max h-max"
                  onClick={() => handleRemovePhoto(index)}
                >
                  <XCircle size={24} />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
