import { useCallback, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { PostgrestError } from "@supabase/supabase-js";

import { questionnaire } from "./questionnaire";
import { generateFullDescription } from "@/lib/generate-full-description";
import { QuestionSelector } from "./QuestionSelector";
import { ProfileFormData } from "./CreateProfileForm";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { CUSTOM_QUESTION } from "./GeneralNewJobForm";
import { sendNotificationEmail } from "@/lib/utils";

export function UnAuthorizedNewJobForm() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get job details based on id or custom title
  const jobDetails = useMemo(() => {
    if (id === "custom") {
      const customTitle = searchParams.get("title");
      return {
        label: customTitle || "Nieuwe klus",
        questions: CUSTOM_QUESTION,
        hasCustomQuestion: false,
        services: [],
      };
    }

    return questionnaire.find((q) => q.id === id);
  }, [id, searchParams]);

  const handleFileUpload = async (
    files: File[],
    jobId: string
  ): Promise<string[]> => {
    try {
      const uploadPromises = files.map(async (file) => {
        const fileExt = file.name.split(".").pop() || "";
        const fileName = `${jobId}/${crypto.randomUUID()}.${fileExt}`;

        const { error: uploadError, data } = await supabase.storage
          .from("job-photos")
          .upload(fileName, file, {
            cacheControl: "3600",
            upsert: false,
          });

        if (uploadError) {
          throw new Error(
            `Failed to upload ${file.name}: ${uploadError.message}`
          );
        }

        const {
          data: { publicUrl },
        } = supabase.storage.from("job-photos").getPublicUrl(fileName);

        return publicUrl;
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error("File upload failed:", error);
      throw new Error("Failed to upload one or more files");
    }
  };

  const handleSubmit = useCallback(
    async ({
      answers,
      photos,
      profile: {
        email,
        password,
        house_number,
        postal_code,
        first_name,
        city,
        last_name,
        house_number_addition,
      },
      services = [],
    }: {
      photos: File[];
      answers: string[];
      profile: ProfileFormData;
      services?: string[];
    }) => {
      setIsSubmitting(true);

      try {
        // Step 1: User Authentication
        const { data: authData, error: signUpError } =
          await supabase.auth.signUp({
            email,
            password,
            options: {
              data: { user_type: "klusaanvrager" },
              emailRedirectTo: `${location.origin}/kies-uw-vakman`,
            },
          });

        if (signUpError) {
          if (signUpError.message.includes("exist")) {
            throw new Error(
              "Dit e-mailadres is al in gebruik. Probeer in te loggen."
            );
          }
          throw new Error("Er ging iets mis bij het aanmaken van uw account.");
        }

        if (!authData.user) {
          throw new Error("Er ging iets mis bij het aanmaken van uw account.");
        }

        const userId = authData.user.id;

        // Step 2: Update Profile
        const { error: profileError } = await supabase
          .from("profiles")
          .update({
            first_name,
            last_name,
            house_number,
            house_number_addition,
            full_name: `${first_name} ${last_name}`.trim(),
            postal_code,
            city,
          })
          .eq("id", userId);

        if (profileError) {
          throw new Error(
            "Er ging iets mis bij het opslaan van uw profielgegevens."
          );
        }

        // Step 3: Handle Photo Upload
        let photoUrls: string[] = [];
        try {
          photoUrls = photos?.length
            ? await handleFileUpload(photos, crypto.randomUUID())
            : [];
        } catch (uploadError) {
          throw new Error(
            "Er ging iets mis bij het uploaden van de foto's. Controleer het formaat en de grootte."
          );
        }

        // Step 4: Create Job
        const { data: jobData, error: jobError } = await supabase
          .from("jobs")
          .insert({
            title: jobDetails.label,
            description: generateFullDescription(
              id,
              answers,
              answers[
                jobDetails?.hasCustomQuestion
                  ? jobDetails.questions.length - 1
                  : jobDetails.questions.length
              ]
            ),
            postal_code,
            house_number,
            user_id: userId,
            photos: photoUrls,
            services: services.length > 0 ? services : jobDetails.services,
          })
          .select()
          .single();

        if (jobError) {
          const error = jobError as PostgrestError;
          if (error.code === "23503") {
            // Foreign key violation
            throw new Error(
              "Er ging iets mis bij het koppelen van de klus aan uw profiel."
            );
          }
          throw new Error("Er ging iets mis bij het aanmaken van de klus.");
        }

        // Success handling
        navigate(`/kies-uw-vakman?id=${jobData.id}`, { replace: true });
        toast({
          title: "Registratie succesvol",
          description:
            "Controleer uw e-mailinbox om uw e-mailadres te verifiëren.",
        });

        // Send user registration notification
        await sendNotificationEmail({
          to: ["<EMAIL>"],
          subject: "Nieuwe gebruiker geregistreerd",
          content: `
            <div style="font-family: sans-serif; color: #333;">
              <h2>Nieuwe gebruiker geregistreerd op Klusgebied</h2>
              <p><strong>Naam:</strong> ${first_name} ${last_name}</p>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Type gebruiker:</strong> klusaanvrager</p>
              <p><a href="${window.location.origin}/beheerder/gebruikers" style="color: #0066cc;">Bekijk in admin panel</a></p>
            </div>
          `,
        });

        // Send new job notification
        await sendNotificationEmail({
          to: ["<EMAIL>"],
          subject: "Nieuwe klusopdracht geplaatst",
          content: `
            <div style="font-family: sans-serif; color: #333;">
              <h2>Nieuwe klusopdracht</h2>
              <p><strong>Titel:</strong> ${jobDetails.label}</p>
              <p><strong>Klant:</strong> ${first_name} ${last_name}</p>
              <p><strong>Locatie:</strong> ${postal_code}</p>
              <p><strong>Email:</strong> ${email}</p>
              <p><a href="${window.location.origin}/beheerder/berichten" style="color: #0066cc;">Bekijk in admin panel</a></p>
            </div>
          `,
        });
      } catch (error) {
        console.error("Job creation failed:", error);
        toast({
          variant: "destructive",
          title: "Er is iets misgegaan",
          description:
            error instanceof Error
              ? error.message
              : "De klus kon niet worden toegevoegd. Probeer het opnieuw.",
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [id, jobDetails?.label, navigate]
  );

  const handleSelectChange = useCallback(
    (
      data: string[],
      files: File[],
      profile: ProfileFormData,
      services?: string[]
    ) => {
      handleSubmit({
        photos: files,
        answers: data,
        profile,
        services: services || [],
      });
    },
    [handleSubmit]
  );

  return (
    <div className="bg-gradient-to-b from-background via-muted/30 to-background min-h-96 items-center px-5 justify-center flex flex-col py-10 max-w-xl mx-auto">
      <h1 className="text-4xl py-20 font-bold text-center">
        {jobDetails.label}
      </h1>
      <QuestionSelector
        questions={jobDetails.questions}
        onValueChange={handleSelectChange}
        isLoading={isSubmitting}
        variant="new"
        hasCustomQuestion={jobDetails?.hasCustomQuestion}
        isCustomJob={id === "custom"}
      />
    </div>
  );
}
