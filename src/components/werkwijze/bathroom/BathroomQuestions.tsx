import { UseFormReturn } from "react-hook-form";

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { QuestionStep } from "./types";

interface QuestionProps {
  step: QuestionStep;
  form: UseFormReturn<any>;
}

export const BathroomQuestions = ({ step, form }: QuestionProps) => {
  switch (step) {
    case QuestionStep.surface:
      return (
        <FormField
          control={form.control}
          name="surfaceArea"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Wat is de oppervlakte van de badkamer (in m²)?
              </FormLabel>
              <FormControl>
                <Input {...field} placeholder="Bijv. 10" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.current:
      return (
        <FormField
          control={form.control}
          name="currentSituation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat is de huidige situatie van je badkamer?</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Beschrijf de huidige staat van je badkamer..."
                  className="min-h-[100px]"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.walls:
      return (
        <FormField
          control={form.control}
          name="wallTiles"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat wil je met de wandtegels?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new" id="walls-new" />
                    <Label htmlFor="walls-new">Nieuwe tegels</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="walls-keep" />
                    <Label htmlFor="walls-keep">Huidige tegels behouden</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="unsure" id="walls-unsure" />
                    <Label htmlFor="walls-unsure">Nog niet zeker</Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.floor:
      return (
        <FormField
          control={form.control}
          name="floorTiles"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat wil je met de vloertegels?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new" id="floor-new" />
                    <Label htmlFor="floor-new">Nieuwe tegels</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="floor-keep" />
                    <Label htmlFor="floor-keep">Huidige tegels behouden</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="unsure" id="floor-unsure" />
                    <Label htmlFor="floor-unsure">Nog niet zeker</Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.toilet:
      return (
        <FormField
          control={form.control}
          name="toilet"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat zijn je wensen voor het toilet?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new" id="toilet-new" />
                    <Label htmlFor="toilet-new">Nieuw toilet</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="toilet-keep" />
                    <Label htmlFor="toilet-keep">Huidig toilet behouden</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="remove" id="toilet-remove" />
                    <Label htmlFor="toilet-remove">
                      Geen toilet in badkamer
                    </Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.shower:
      return (
        <FormField
          control={form.control}
          name="shower"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat zijn je wensen voor de douche?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new-cabin" id="shower-cabin" />
                    <Label htmlFor="shower-cabin">Nieuwe douchecabine</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="walk-in" id="shower-walk-in" />
                    <Label htmlFor="shower-walk-in">Inloopdouche</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="shower-keep" />
                    <Label htmlFor="shower-keep">Huidige douche behouden</Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.bath:
      return (
        <FormField
          control={form.control}
          name="bath"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat zijn je wensen voor een bad?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new" id="bath-new" />
                    <Label htmlFor="bath-new">Nieuw bad</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="bath-keep" />
                    <Label htmlFor="bath-keep">Huidig bad behouden</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="remove" id="bath-remove" />
                    <Label htmlFor="bath-remove">Geen bad gewenst</Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.sink:
      return (
        <FormField
          control={form.control}
          name="sink"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat zijn je wensen voor de wastafel?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="single" id="sink-single" />
                    <Label htmlFor="sink-single">Enkele wastafel</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="double" id="sink-double" />
                    <Label htmlFor="sink-double">Dubbele wastafel</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="sink-keep" />
                    <Label htmlFor="sink-keep">Huidige wastafel behouden</Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.ventilation:
      return (
        <FormField
          control={form.control}
          name="ventilation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat zijn je wensen voor de ventilatie?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="new" id="vent-new" />
                    <Label htmlFor="vent-new">Nieuwe ventilatie</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="vent-keep" />
                    <Label htmlFor="vent-keep">
                      Huidige ventilatie behouden
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="unsure" id="vent-unsure" />
                    <Label htmlFor="vent-unsure">Advies gewenst</Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.heating:
      return (
        <FormField
          control={form.control}
          name="heating"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Wat zijn je wensen voor de verwarming?</FormLabel>
              <FormControl>
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="floor" id="heat-floor" />
                    <Label htmlFor="heat-floor">Vloerverwarming</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="radiator" id="heat-radiator" />
                    <Label htmlFor="heat-radiator">Radiator</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="keep" id="heat-keep" />
                    <Label htmlFor="heat-keep">
                      Huidige verwarming behouden
                    </Label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.additional:
      return (
        <FormField
          control={form.control}
          name="additionalInfo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Heb je nog aanvullende wensen of opmerkingen?
              </FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Bijv. specifieke materiaalvoorkeuren, kleurwensen of andere belangrijke details..."
                  className="min-h-[100px]"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      );

    case QuestionStep.account:
      return (
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Beschrijf de gewenste badkamer renovatie</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Beschrijf de situatie zo duidelijk mogelijk..."
                    className="h-24"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Wachtwoord</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="password"
                    placeholder="Minimaal 6 karakters"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="postalCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Postcode</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="1234 AB"
                    className="uppercase"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="houseNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Huisnummer</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="12" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      );

    default:
      return null;
  }
};
