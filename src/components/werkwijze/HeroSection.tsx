import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Search } from "lucide-react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { questionnaire } from "./new-jobs/questionnaire";

export const HeroSection = () => {
  const navigate = useNavigate();
  const [value, setValue] = useState("");

  const filteredItems = useMemo(() => {
    if (!value) return questionnaire;

    const predefinedItems = questionnaire.filter((item) =>
      item.label.toLowerCase().includes(value.toLowerCase())
    );

    // Only add custom item if input doesn't exactly match any predefined items
    const exactMatch = predefinedItems.some(
      (item) => item.label.toLowerCase() === value.toLowerCase()
    );

    if (!exactMatch && value.length > 2) {
      return [
        ...predefinedItems,
        { id: "custom", label: value, questions: [], icon: Search },
      ];
    }

    return predefinedItems;
  }, [value]);

  return (
    <div className="relative isolate px-6 pt-4 lg:px-8">
      <div className="mx-auto max-w-2xl py-8 sm:py-16 lg:py-24">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl font-display">
            Vind de juiste vakman voor jouw klus
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Beschrijf je klus en ontvang snel reacties van gekwalificeerde
            vakmensen in jouw buurt.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <div className="relative w-full max-w-lg">
              <Command
                filter={(value, search) => {
                  if (!search) return 1;
                  const searchValue = search.toLowerCase().trim();
                  const itemValue = value.toLowerCase().trim();
                  return itemValue.includes(searchValue) ? 1 : 0;
                }}
                className="rounded-lg border shadow-md"
              >
                <CommandInput
                  placeholder="Waar ben je naar op zoek? Bijv: lekkage verhelpen"
                  value={value}
                  onValueChange={setValue}
                  className="h-12"
                />
                <CommandList className="max-h-[300px] overflow-y-auto">
                  <CommandEmpty>Geen suggesties gevonden</CommandEmpty>
                  <CommandGroup heading="Populaire klussen">
                    {filteredItems.map(({ id, label, icon: Icon }, idx) => (
                      <CommandItem
                        key={id === "custom" ? "custom" : idx}
                        value={label}
                        onSelect={() => {
                          navigate(
                            id === "custom"
                              ? `/plaats-een-klus/custom?title=${encodeURIComponent(
                                  label
                                )}`
                              : `/plaats-een-klus/${id}`
                          );
                        }}
                        className="cursor-pointer py-3 px-4 hover:bg-secondary"
                      >
                        {Icon && <Icon className="mr-2 h-4 w-4 text-primary" />}
                        {label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
