import { ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { But<PERSON> } from "@/components/ui/button";

export const CtaSection = () => {
  const navigate = useNavigate();

  return (
    <div className="bg-white py-16 md:py-24">
      <div
        className="container mx-auto px-4 text-center max-w-4xl animate-fade-in"
        style={{ animationDelay: "1s" }}
      >
        <h2 className="text-2xl md:text-3xl font-display font-semibold mb-6 text-gray-900">
          Klaar om je klus te laten uitvoeren?
        </h2>
        <p className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
          Start vandaag nog met het vinden van de perfecte vakman voor jouw
          klus. Binnen no-time weet je waar je aan toe bent.
        </p>
        <Button
          onClick={() => navigate("/auth")}
          size="lg"
          className="bg-primary hover:bg-primary-hover text-white font-semibold text-lg inline-flex items-center gap-2 group px-8 py-6"
        >
          Start direct
          <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
        </Button>
      </div>
    </div>
  );
};
