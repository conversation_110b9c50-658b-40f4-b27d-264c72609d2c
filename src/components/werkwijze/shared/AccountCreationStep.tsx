import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface AccountCreationStepProps {
  email: string;
  setEmail: (value: string) => void;
  password: string;
  setPassword: (value: string) => void;
  confirmPassword: string;
  setConfirmPassword: (value: string) => void;
  postalCode: string;
  setPostalCode: (value: string) => void;
  houseNumber: string;
  setHouseNumber: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  preferredDate: string;
  setPreferredDate: (value: string) => void;
  additionalNotes: string;
  setAdditionalNotes: (value: string) => void;
}

export const AccountCreationStep = ({
  email,
  setEmail,
  password,
  setPassword,
  confirmPassword,
  setConfirmPassword,
  postalCode,
  setPostalCode,
  houseNumber,
  setHouseNumber,
  description,
  setDescription,
  preferredDate,
  setPreferredDate,
  additionalNotes,
  setAdditionalNotes,
}: AccountCreationStepProps) => {
  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Label htmlFor="description">Projectbeschrijving</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Beschrijf de werkzaamheden zo duidelijk mogelijk..."
          className="h-24"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="password">Wachtwoord</Label>
        <Input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Minimaal 6 karakters"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="confirmPassword">Bevestig wachtwoord</Label>
        <Input
          id="confirmPassword"
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          placeholder="Herhaal wachtwoord"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="postalCode">Postcode</Label>
        <Input
          id="postalCode"
          value={postalCode}
          onChange={(e) => setPostalCode(e.target.value.toUpperCase())}
          placeholder="1234 AB"
          className="uppercase"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="houseNumber">Huisnummer</Label>
        <Input
          id="houseNumber"
          value={houseNumber}
          onChange={(e) => setHouseNumber(e.target.value)}
          placeholder="12"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="preferredDate">Voorkeursdatum</Label>
        <Input
          id="preferredDate"
          type="date"
          value={preferredDate}
          onChange={(e) => setPreferredDate(e.target.value)}
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="additionalNotes">Extra opmerkingen</Label>
        <Textarea
          id="additionalNotes"
          value={additionalNotes}
          onChange={(e) => setAdditionalNotes(e.target.value)}
          placeholder="Eventuele extra informatie..."
          className="h-20"
        />
      </div>

      <div className="pt-4">
        <p className="text-sm text-muted-foreground mb-4">
          Klik op 'Verstuur aanvraag' om een account aan te maken en je aanvraag in te dienen.
        </p>
      </div>
    </div>
  );
};