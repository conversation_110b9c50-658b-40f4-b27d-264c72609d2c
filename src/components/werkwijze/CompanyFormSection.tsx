import { useState, FormEvent } from "react";
import { Check } from "lucide-react";

import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

const CompanyFormSection = () => {
  const { toast } = useToast();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    companyName: "",
    personnelType: "",
    kvk: "",
    personnelCount: "",
  });

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // First, insert the form data into company_forms table
      const { error: insertError } = await supabase
        .from("company_forms")
        .insert({
          name: formData.name,
          company_name: formData.companyName,
          personnel_type: formData.personnelType,
          kvk_number: formData.kvk,
          personnel_count: formData.personnelCount,
        });

      if (insertError) {
        throw new Error("Er ging iets mis bij het opslaan van uw aanvraag");
      }

      const emailContent = {
        to: ["<EMAIL>"],
        subject: "Nieuwe personeelsaanvraag",
        html: `
          <h2>Nieuwe personeelsaanvraag</h2>
          <p><strong>Naam:</strong> ${formData.name}</p>
          <p><strong>Bedrijfsnaam:</strong> ${formData.companyName}</p>
          <p><strong>Type personeel:</strong> ${formData.personnelType}</p>
          <p><strong>KVK nummer:</strong> ${formData.kvk}</p>
          <p><strong>Aantal medewerkers:</strong> ${formData.personnelCount}</p>
        `,
      };

      const { error: emailError } = await supabase.functions.invoke(
        "send-email",
        {
          body: emailContent,
        }
      );

      if (emailError) {
        throw new Error("Er ging iets mis bij het versturen van het formulier");
      }

      // Success handling
      toast({
        title: "Aanvraag verstuurd!",
        description: "We nemen zo spoedig mogelijk contact met u op.",
      });

      // Reset form
      setFormData({
        name: "",
        companyName: "",
        personnelType: "",
        kvk: "",
        personnelCount: "",
      });
    } catch (error) {
      console.error("Form submission error:", error);
      toast({
        title: "Fout bij versturen",
        description:
          error instanceof Error
            ? error.message
            : "Er ging iets mis bij het versturen van het formulier",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <section className="container mx-auto px-4 max-w-6xl">
      <div className="py-16 md:py-24">
        {/* Hero Section with enhanced styling */}
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h1 className="text-3xl md:text-5xl font-display font-semibold mb-6">
            Personeelstekort? Wij hebben de vakmannen.
          </h1>

          <p className="text-lg text-gray-600 mb-12">
            Of u nu met spoed een vakman nodig heeft of op zoek bent naar
            versterking voor langere tijd – bij Klusgebied.nl vindt u direct de
            juiste mensen.
          </p>

          {/* Benefits list with enhanced visual hierarchy */}
          <div className="grid md:grid-cols-2 gap-4 text-left max-w-2xl mx-auto mb-12">
            <div className="flex items-start gap-3 p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <Check className="w-6 h-6 text-primary shrink-0" />
              <span className="text-gray-700">
                Meer dan 40.000 gecertificeerde vakmannen
              </span>
            </div>
            <div className="flex items-start gap-3 p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <Check className="w-6 h-6 text-primary shrink-0" />
              <span className="text-gray-700">
                Snel inzetbaar – door heel Nederland
              </span>
            </div>
            <div className="flex items-start gap-3 p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <Check className="w-6 h-6 text-primary shrink-0" />
              <span className="text-gray-700">
                Gecontroleerde profielen met ervaring & VCA
              </span>
            </div>
            <div className="flex items-start gap-3 p-4 rounded-lg hover:bg-gray-50 transition-colors">
              <Check className="w-6 h-6 text-primary shrink-0" />
              <span className="text-gray-700">
                Van timmerman tot loodgieter, allround klusser tot stukadoor
              </span>
            </div>
          </div>
        </div>

        {/* Enhanced Form Section */}
        <div className="bg-gradient-to-b from-white to-gray-50 rounded-3xl shadow-2xl p-6 md:p-12 max-w-2xl mx-auto border border-gray-100">
          <h2 className="text-2xl font-display font-semibold text-center mb-8 bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Vraag vandaag nog gratis personeel aan
          </h2>

          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="grid md:grid-cols-2 gap-4 md:gap-6">
              <div className="space-y-2 group">
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700"
                >
                  Naam
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border-2 border-gray-100 rounded-xl focus:ring-4 focus:ring-primary/10 focus:border-primary transition-all bg-white shadow-sm group-hover:border-gray-200"
                  required
                />
              </div>

              {/* Update other input fields similarly with name attribute and onChange handler */}
              <div className="space-y-2 group">
                <label
                  htmlFor="companyName"
                  className="block text-sm font-medium text-gray-700"
                >
                  Bedrijfsnaam
                </label>
                <input
                  type="text"
                  id="companyName"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border-2 border-gray-100 rounded-xl focus:ring-4 focus:ring-primary/10 focus:border-primary transition-all bg-white shadow-sm group-hover:border-gray-200"
                  required
                />
              </div>

              <div className="space-y-2 md:col-span-2 group">
                <label
                  htmlFor="personnelType"
                  className="block text-sm font-medium text-gray-700"
                >
                  Wat voor personeel zoekt u?
                </label>
                <input
                  type="text"
                  id="personnelType"
                  name="personnelType"
                  value={formData.personnelType}
                  onChange={handleChange}
                  placeholder="Bijv: Timmerman, Loodgieter, Elektricien"
                  className="w-full px-4 py-3 border-2 border-gray-100 rounded-xl focus:ring-4 focus:ring-primary/10 focus:border-primary transition-all bg-white shadow-sm group-hover:border-gray-200"
                  required
                />
              </div>

              <div className="space-y-2 group">
                <label
                  htmlFor="kvk"
                  className="block text-sm font-medium text-gray-700"
                >
                  KVK nummer
                </label>
                <input
                  type="text"
                  id="kvk"
                  name="kvk"
                  value={formData.kvk}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border-2 border-gray-100 rounded-xl focus:ring-4 focus:ring-primary/10 focus:border-primary transition-all bg-white shadow-sm group-hover:border-gray-200"
                  required
                />
              </div>

              {/* Personnel Count Radio Buttons */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700">
                  Hoeveel personeel zoekt u?
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {["1-5", "5-10", "10-50", "100+"].map((option) => (
                    <label
                      key={option}
                      className="relative flex items-center gap-2 p-3 border-2 border-gray-100 rounded-xl cursor-pointer hover:border-primary/50 transition-all bg-white shadow-sm group"
                    >
                      <input
                        type="radio"
                        name="personnelCount"
                        value={option}
                        checked={formData.personnelCount === option}
                        onChange={handleChange}
                        className="form-radio text-primary focus:ring-primary/20 border-2"
                        required
                      />
                      <span className="text-sm font-medium">{option}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-primary text-white text-lg font-semibold py-4 px-6 rounded-xl hover:bg-primary/90 transform hover:-translate-y-0.5 transition-all duration-200 shadow-lg hover:shadow-xl mt-8 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? "Bezig met versturen..." : "Direct aanvragen"}
            </button>
          </form>
        </div>
      </div>
    </section>
  );
};

export default CompanyFormSection;

// import { Check } from "lucide-react";

// const CompanyFormSection = () => {
//   return (
//     <section className="container mx-auto px-4 max-w-6xl">
//       <div className="py-16 text-center">
//         <h1 className="text-2xl md:text-4xl font-display font-semibold mb-6">
//           Personeelstekort? Wij hebben de vakmannen.
//         </h1>

//         <p className="text-gray-600 mb-8">
//           Of u nu met spoed een vakman nodig heeft of op zoek bent naar
//           versterking voor langere tijd – bij Klusgebied.nl vindt u direct de
//           juiste mensen.
//         </p>

//         <div className="my-8 text-left max-w-2xl mx-auto space-y-3">
//           <p className="flex items-start gap-2">
//             <Check className="w-5 h-5 text-primary shrink-0" />
//             <span>Meer dan 40.000 gecertificeerde vakmannen</span>
//           </p>
//           <p className="flex items-start gap-2">
//             <Check className="w-5 h-5 text-primary shrink-0" />
//             <span>Snel inzetbaar – door heel Nederland</span>
//           </p>
//           <p className="flex items-start gap-2">
//             <Check className="w-5 h-5 text-primary shrink-0" />
//             <span>Gecontroleerde profielen met ervaring & VCA</span>
//           </p>
//           <p className="flex items-start gap-2">
//             <Check className="w-5 h-5 text-primary shrink-0" />
//             <span>
//               Van timmerman tot loodgieter, allround klusser tot stukadoor
//             </span>
//           </p>
//         </div>

//         <h2 className="text-xl md:text-2xl font-semibold mt-8">
//           Vraag vandaag nog gratis personeel aan – wij regelen de rest.
//         </h2>

//         <form className="mt-12 bg-white shadow-xl rounded-2xl p-8 max-w-2xl mx-auto">
//           <div className="grid md:grid-cols-2 gap-6">
//             <div className="space-y-2">
//               <label
//                 htmlFor="name"
//                 className="block text-sm font-medium text-gray-700 text-left"
//               >
//                 Naam
//               </label>
//               <input
//                 type="text"
//                 id="name"
//                 className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
//                 required
//               />
//             </div>

//             <div className="space-y-2">
//               <label
//                 htmlFor="companyName"
//                 className="block text-sm font-medium text-gray-700 text-left"
//               >
//                 Bedrijfsnaam
//               </label>
//               <input
//                 type="text"
//                 id="companyName"
//                 className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
//                 required
//               />
//             </div>

//             <div className="space-y-2 md:col-span-2">
//               <label
//                 htmlFor="personnelType"
//                 className="block text-sm font-medium text-gray-700 text-left"
//               >
//                 Wat voor personeel zoekt u?
//               </label>
//               <input
//                 type="text"
//                 id="personnelType"
//                 className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
//                 required
//               />
//             </div>

//             <div className="space-y-2">
//               <label
//                 htmlFor="kvk"
//                 className="block text-sm font-medium text-gray-700 text-left"
//               >
//                 KVK nummer
//               </label>
//               <input
//                 type="text"
//                 id="kvk"
//                 className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
//                 required
//               />
//             </div>

//             <div className="space-y-2">
//               <label className="block text-sm font-medium text-gray-700 text-left">
//                 Hoeveel personeel zoekt u?
//               </label>
//               <div className="grid grid-cols-2 gap-2">
//                 {["1-5", "5-10", "10-50", "100+"].map((option) => (
//                   <label
//                     key={option}
//                     className="flex items-center space-x-2 cursor-pointer"
//                   >
//                     <input
//                       type="radio"
//                       name="personnelCount"
//                       value={option}
//                       className="form-radio text-primary focus:ring-primary"
//                       required
//                     />
//                     <span className="text-sm">{option}</span>
//                   </label>
//                 ))}
//               </div>
//             </div>
//           </div>

//           <button
//             type="submit"
//             className="mt-8 w-full bg-primary text-white font-semibold py-3 px-6 rounded-lg hover:bg-primary/90 transition-colors"
//           >
//             Verstuur aanvraag
//           </button>
//         </form>
//       </div>
//     </section>
//   );
// };

// export default CompanyFormSection;
