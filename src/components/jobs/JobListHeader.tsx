import { Briefcase } from "lucide-react";

import { BackToDashboard } from "../BackToDashboard";

interface JobListHeaderProps {
  userType: string | null;
}

export const JobListHeader = ({ userType }: JobListHeaderProps) => {
  return (
    <div className="space-y-6 animate-fade-in">
      <BackToDashboard />
      <div className="flex sm:items-center items-start gap-4">
        <div className="bg-primary/10 p-3 rounded-lg">
          <Briefcase className="h-8 w-8 text-primary" />
        </div>
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold text-foreground">
            {userType === "vakman" ? "Beschikbare Klussen" : "<PERSON><PERSON>sen"}
          </h2>
          <p className="text-muted-foreground mt-1">
            {userType === "vakman"
              ? "Bekijk en reageer op beschikbare klussen"
              : "<PERSON><PERSON><PERSON> je gepla<PERSON>te klussen"}
          </p>
        </div>
      </div>
    </div>
  );
};
