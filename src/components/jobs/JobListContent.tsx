import { useEffect, useState } from "react";
import { SearchX } from "lucide-react";

import { JobCard } from "@/components/JobCard";
import type { Job } from "@/types/database/tables/jobs";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "../auth/hooks/useAuth";

interface JobListContentProps {
  jobs: Job["Row"][];
  isLoading?: boolean;
  userType?: string;
  searchQuery?: string;
  onJobClick: (job: Job["Row"]) => void;
}

export const JobListContent = ({
  jobs,
  isLoading = false,
  userType,
  searchQuery = "",
  onJobClick,
}: JobListContentProps) => {
  const { userProfile } = useAuth();

  const [jobsWithResponseCount, setJobsWithResponseCount] = useState<
    (Job["Row"] & { responseCount: number })[]
  >([]);
  const [responseLoading, setResponseLoading] = useState(false);

  useEffect(() => {
    const fetchResponseCounts = async () => {
      setResponseLoading(true);
      try {
        const jobIds = jobs.map((job) => job.id);
        const { data: responses, error } = await supabase
          .from("job_responses")
          .select("job_id, id")
          .in("job_id", jobIds);

        if (error) throw error;

        const responseCounts = responses?.reduce((acc, response) => {
          acc[response.job_id] = (acc[response.job_id] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        const jobsWithCounts = jobs.map((job) => ({
          ...job,
          responseCount: responseCounts[job.id] || 0,
          isDirect: job.direct_request?.includes(userProfile?.id),
        }));

        // Sort jobs in three tiers:
        // 1. Direct requests first
        // 2. Jobs with responses second
        // 3. Remaining jobs last
        const sortedJobs = jobsWithCounts.sort((a, b) => {
          // First compare isDirect
          if (a.isDirect !== b.isDirect) {
            return a.isDirect ? -1 : 1;
          }

          // Then compare if they have responses
          if (a.responseCount > 0 !== b.responseCount > 0) {
            return b.responseCount > 0 ? 1 : -1;
          }

          // If in same category, sort by response count
          if (userType === "klusaanvrager") {
            return b.responseCount - a.responseCount;
          }

          // For other user types, maintain original order
          return 0;
        });

        setJobsWithResponseCount(sortedJobs);
      } catch (error) {
        console.error("Error fetching response counts:", error);
      } finally {
        setResponseLoading(false);
      }
    };

    if (jobs.length > 0) {
      fetchResponseCounts();
    } else {
      setJobsWithResponseCount([]);
    }
  }, [jobs, userType, userProfile?.id]);

  const filterJobs = (jobs: (Job["Row"] & { responseCount: number })[]) => {
    if (!searchQuery) return jobs;

    const query = searchQuery.toLowerCase();
    return jobs.filter((job) => {
      return (
        job.title?.toLowerCase().includes(query) ||
        job.description?.toLowerCase().includes(query) ||
        job.postal_code?.toLowerCase().includes(query)
      );
    });
  };

  const filteredResults = filterJobs(jobsWithResponseCount);

  if (isLoading || responseLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 h-48 rounded-t-lg" />
            <div className="bg-white p-4 rounded-b-lg space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="h-4 bg-gray-200 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (
    !jobsWithResponseCount.length ||
    (searchQuery && !filteredResults.length)
  ) {
    return (
      <div className="text-center py-16 px-4">
        <div className="max-w-md mx-auto">
          {searchQuery ? (
            <>
              <div className="flex justify-center mb-4">
                <SearchX className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Geen resultaten gevonden
              </h3>
              <p className="text-gray-600 mb-6">
                We kunnen geen klussen vinden die overeenkomen met "
                {searchQuery}".
              </p>
              <div className="space-y-3 text-left bg-gray-50 p-4 rounded-lg">
                <p className="text-sm font-medium text-gray-900">Suggesties:</p>
                <ul className="text-sm text-gray-600 list-disc pl-5 space-y-1">
                  <li>Controleer op spelfouten</li>
                  <li>Gebruik minder specifieke zoektermen</li>
                  <li>Probeer gerelateerde zoekwoorden</li>
                  <li>Zoek op postcode of plaats</li>
                </ul>
              </div>
            </>
          ) : (
            <>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Geen klussen beschikbaar
              </h3>
              <p className="text-gray-600">
                Er zijn momenteel geen klussen beschikbaar. Kom later terug voor
                nieuwe mogelijkheden.
              </p>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 animate-fade-in">
      {filteredResults.map((job) => (
        <JobCard
          key={job.id}
          id={job.id}
          title={job.title}
          description={job.description}
          status={job.status || undefined}
          photos={job.photos}
          isDirect={job.isDirect}
          onClick={() => onJobClick(job)}
          job={job}
        />
      ))}
    </div>
  );
};
