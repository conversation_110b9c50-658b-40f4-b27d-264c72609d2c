import { useState } from "react";
import { <PERSON> } from "lucide-react";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface JobReviewProps {
  review: {
    id: string;
    rating: number;
    comment?: string;
    feedback?: string | null;
    reviewer?: {
      first_name: string | null;
      last_name: string | null;
    };
    created_at: string;
    vakman_id: string;
  };
}

export const JobReview = ({ review }: JobReviewProps) => {
  const [feedback, setFeedback] = useState(review.feedback || ""); // Local state for feedback
  const [isReplying, setIsReplying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { toast } = useToast();

  const reviewerName = review.reviewer
    ? `${review.reviewer.first_name} ${review.reviewer.last_name}`
    : "Anoniem";

  const handleSubmitResponse = async () => {
    setIsLoading(true);
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        toast({
          variant: "destructive",
          title: "Niet ingelogd",
          description: "Je moet ingelogd zijn om te kunnen reageren.",
        });
        return;
      }

      if (userData.user.id !== review.vakman_id) {
        toast({
          variant: "destructive",
          title: "Geen toegang",
          description: "Je kunt alleen reageren op je eigen reviews.",
        });
        return;
      }

      const { error } = await supabase
        .from("vakman_reviews")
        .update({ feedback: feedback.trim() })
        .eq("id", review.id);

      if (error) {
        console.error("Error submitting response:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description:
            "Er is een fout opgetreden bij het plaatsen van je reactie.",
        });
      } else {
        setIsReplying(false);
        toast({
          title: "Reactie geplaatst",
          description: "Je reactie is succesvol geplaatst.",
        });
      }
    } catch (error) {
      console.error("Error:", error);
      toast({
        variant: "destructive",
        title: "Fout",
        description: "Er is een onverwachte fout opgetreden.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Beoordeling</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, index) => (
            <Star
              key={index}
              className={`h-5 w-5 ${
                index < review.rating
                  ? "fill-yellow-400 text-yellow-400"
                  : "fill-gray-200 text-gray-200"
              }`}
            />
          ))}
        </div>
        {review.comment && (
          <p className="text-muted-foreground">{review.comment}</p>
        )}
        <p className="text-sm text-muted-foreground">
          Beoordeeld door {reviewerName} op{" "}
          {new Date(review.created_at).toLocaleDateString("nl-NL")}
        </p>

        {feedback && !isReplying && (
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <p className="font-medium text-sm mb-2">Reactie van de vakman:</p>
            <p className="text-muted-foreground">{feedback}</p>
          </div>
        )}

        {!isReplying && !feedback && (
          <Button
            onClick={() => setIsReplying(true)}
            variant="outline"
            className="w-full"
          >
            Reageer op deze review
          </Button>
        )}

        {isReplying && (
          <div className="space-y-4">
            <Textarea
              placeholder="Schrijf hier je reactie..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex gap-2">
              <Button
                onClick={handleSubmitResponse}
                disabled={isLoading || !feedback.trim()}
              >
                Plaats reactie
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsReplying(false)}
                disabled={isLoading}
              >
                Annuleren
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
