import { Use<PERSON>, <PERSON>, Phone, Eye } from "lucide-react";

import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";

interface VakmanInfoProps {
  vakmanProfile: any;
  setShowVakmanProfile: (show: boolean) => void;
}

export const VakmanInfo = ({
  vakmanProfile,
  setShowVakmanProfile,
}: VakmanInfoProps) => {
  if (!vakmanProfile) return null;

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Vakman Details</h2>
      <Card className="p-4">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={vakmanProfile.profile_photo_url} />
              <AvatarFallback>
                <User className="h-6 w-6" />
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-medium">
                {vakmanProfile.company_name ||
                  `${vakmanProfile.first_name} ${vakmanProfile.last_name}`}
              </h3>
              {vakmanProfile.company_name && (
                <p className="text-sm text-muted-foreground">
                  {vakmanProfile.first_name} {vakmanProfile.last_name}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <Mail className="h-4 w-4" />
              {vakmanProfile.email}
            </p>
            {vakmanProfile.phone_number && (
              <p className="text-sm text-muted-foreground flex items-center gap-2">
                <Phone className="h-4 w-4" />
                {vakmanProfile.phone_number}
              </p>
            )}
          </div>

          <Button
            onClick={() => setShowVakmanProfile(true)}
            className="w-full flex items-center justify-center gap-2"
          >
            <Eye className="h-4 w-4" />
            Bekijk volledig profiel
          </Button>
        </div>
      </Card>
    </div>
  );
};
