import { Button } from "@/components/ui/button";

interface MobileReactionButtonProps {
  count: number;
  isShow: boolean;
  onClick: () => void;
}

export const MobileReactionButton = ({
  count,
  isShow,
  onClick,
}: MobileReactionButtonProps) => {
  return (
    <Button
      variant="outline"
      className="w-full md:hidden flex items-center justify-center gap-2 p-4"
      onClick={onClick}
    >
      <span className="font-medium">
        {isShow ? "Verberg" : "Toon"} alle reacties
      </span>
      <span className="flex items-center justify-center w-6 h-6 text-sm font-semibold bg-white text-primary border border-primary/20 rounded-md shadow-sm">
        {count}
      </span>
    </Button>
  );
};
