import React, { useEffect, useRef, useState } from "react";
import {
  QrCode,
  Shield,
  Smartphone,
  ChevronRight,
  ArrowLeft,
  KeyRound,
  AlertCircle,
  Phone,
  MessageSquare,
  ShieldCheck,
  Loader2,
  Send,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { USER_TYPES } from "@/config/routes";

export const NewMFA: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { userProfile } = useAuth();

  // Admin detection
  const isAdmin = userProfile?.user_type === USER_TYPES.ADMIN;

  // Common state
  const [factorId, setFactorId] = useState("");
  const [enabled, setEnabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [challengeId, setChallengeId] = useState("");

  // Phone MFA state (for non-admin users)
  const [phoneNumber, setPhoneNumber] = useState("");
  const [verifyCode, setVerifyCode] = useState("");
  const [step, setStep] = useState<"phone" | "verify">("phone");

  // TOTP MFA state (for admin users)
  const [totpStep, setTotpStep] = useState<"initial" | "verify">("initial");
  const [verificationCode, setVerificationCode] = useState("");
  const [qr, setQR] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);

  const enrollRef = useRef(false);

  // Helper function to get email name from user's email
  const getEmailName = async (): Promise<string> => {
    if (userProfile?.email) {
      return userProfile.email.split("@")[0];
    }
    return "user";
  };

  const checkMFAStatus = async () => {
    setIsLoading(true);
    try {
      const { data, error } =
        await supabase.auth.mfa.getAuthenticatorAssuranceLevel();
      if (error) throw error;

      setEnabled(data.nextLevel === "aal2");
    } catch (error) {
      console.error("Error checking MFA status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkMFAStatus();
  }, []);

  useEffect(() => {
    if (isAdmin) {
      enrollTOTP();
    } else {
      if (!factorId?.length) return;
      onSendOTPClicked();
    }
  }, [factorId, isLoading, enabled, isAdmin]);

  // TOTP enrollment for admin users
  const enrollTOTP = async () => {
    if (isLoading || enabled || enrollRef.current) return;

    enrollRef.current = true;
    try {
      const emailName = await getEmailName();
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: "totp",
        friendlyName: emailName,
      });
      if (error) throw error;

      setFactorId(data.id);
      setQR(data.totp.qr_code);
    } catch (error) {
      console.error("Error enrolling TOTP MFA:", error);
      toast({
        variant: "destructive",
        title: "Instellen Mislukt",
        description: "Initialiseren van twee-factor-authenticatie mislukt",
      });
    }
  };

  const onCancelled = () => {
    navigate(-1);
  };

  // TOTP verification handlers for admin users
  const handleTOTPCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    setVerificationCode(value);
  };

  const handleTOTPEnable = async () => {
    if (!factorId || !verificationCode) {
      toast({
        variant: "destructive",
        title: "Verificatie Mislukt",
        description: "Voer de verificatiecode in",
      });
      return;
    }

    setIsVerifying(true);
    try {
      const { data: challengeData, error: challengeError } =
        await supabase.auth.mfa.challenge({
          factorId,
        });

      if (challengeError) {
        throw new Error(`Challenge creation failed: ${challengeError.message}`);
      }

      const { error: verifyError } = await supabase.auth.mfa.verify({
        factorId,
        challengeId: challengeData.id,
        code: verificationCode,
      });

      if (verifyError) {
        throw new Error(`Verification failed: ${verifyError.message}`);
      }

      toast({
        title: "2FA Ingeschakeld",
        description: "Twee-factor-authenticatie is succesvol ingeschakeld",
      });
      navigate("/profiel");
    } catch (error) {
      console.error("TOTP MFA enablement failed:", error);
      toast({
        variant: "destructive",
        title: "Instellen Mislukt",
        description:
          error instanceof Error
            ? error.message
            : "Twee-factor-authenticatie inschakelen is mislukt",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  // Phone MFA handlers for non-admin users
  const onEnrollClicked = async () => {
    setError("");
    try {
      const emailName = await getEmailName();
      const factor = await supabase.auth.mfa.enroll({
        phone: phoneNumber,
        factorType: "phone",
        friendlyName: emailName,
      });
      if (factor.error) {
        setError(factor.error.message);
        throw factor.error;
      }
      setFactorId(factor.data.id);
      setStep("verify");
    } catch (error) {
      setError("Failed to Enroll the Factor.");
    }
  };

  const onSendOTPClicked = async () => {
    setError("");
    try {
      const challenge = await supabase.auth.mfa.challenge({ factorId });
      if (challenge.error) {
        setError(challenge.error.message);
        throw challenge.error;
      }
      setChallengeId(challenge.data.id);
    } catch (error) {
      setError("Failed to resend the code.");
    }
  };

  const onEnableClicked = () => {
    setError("");
    (async () => {
      const verify = await supabase.auth.mfa.verify({
        factorId,
        challengeId,
        code: verifyCode,
      });
      if (verify.error) {
        setError(verify.error.message);
        throw verify.error;
      }

      toast({
        title: "2FA Ingeschakeld",
        description: "Twee-factor-authenticatie is succesvol ingeschakeld",
      });
      navigate("/profiel");
    })();
  };

  const formatDutchPhoneNumber = (input: string): string => {
    // Remove all non-numeric characters except '+'
    let cleaned = input.replace(/[^\d+]/g, "");

    // If number doesn't start with '+31' or '0', add '+31'
    if (!cleaned.startsWith("+31") && !cleaned.startsWith("0")) {
      cleaned = "+31" + cleaned;
    }

    // If number starts with '0', replace it with '+31'
    if (cleaned.startsWith("0")) {
      cleaned = "+31" + cleaned.substring(1);
    }

    // Ensure the number is in correct format
    if (cleaned.startsWith("+31") && cleaned.length > 12) {
      cleaned = cleaned.substring(0, 12);
    }

    return cleaned;
  };

  return (
    <div className="min-h-[400px] w-full max-w-lg mx-auto mt-10 p-8 bg-white rounded-xl sm:shadow-lg dark:bg-gray-800 dark:text-white">
      <div className="flex flex-col gap-4 mb-4">
        <button
          onClick={() => navigate(-1)}
          className="self-start flex items-center gap-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Terug</span>
        </button>

        <div className="flex items-center gap-3">
          <Shield className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-semibold">Twee-factor-authenticatie</h2>
        </div>
      </div>

      {enabled ? (
        <div className="space-y-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-800">
                <Shield className="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium text-green-800 dark:text-green-400">
                  2FA is Ingeschakeld
                </h3>
                <p className="text-green-700 dark:text-green-300 mt-1">
                  Twee-factor-authenticatie is al ingesteld voor je account.
                </p>
              </div>
            </div>
          </div>

          <button
            onClick={() => navigate(-1)}
            className="w-full py-3 px-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 
                   transition-colors duration-200 font-medium dark:bg-gray-700 dark:text-gray-300
                   inline-flex items-center justify-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Terug naar instellingen
          </button>
        </div>
      ) : (
        <>
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-600">
              <p className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5" />
                {error}
              </p>
            </div>
          )}

          {isAdmin ? (
            // TOTP MFA UI for admin users
            <>
              {totpStep === "initial" && (
                <div className="space-y-6">
                  <div className="flex flex-col items-center">
                    <div className="relative w-64 h-64 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      {qr ? (
                        <img
                          src={qr}
                          alt="QR Code"
                          className="w-full h-full object-contain"
                        />
                      ) : (
                        <>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <QrCode className="w-12 h-12 text-gray-400" />
                          </div>
                          <div className="w-full h-full flex items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                            QR Code
                          </div>
                        </>
                      )}
                    </div>
                    <button className="mt-4 text-primary hover:text-primary-hover text-sm font-medium hover:underline inline-flex items-center gap-2">
                      <KeyRound className="w-4 h-4" />
                      Handmatige setupcode
                    </button>
                  </div>

                  <div className="space-y-4 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 className="font-medium text-lg">
                      Installatie-instructies
                    </h3>
                    <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                      <li className="flex items-start gap-3">
                        <Smartphone className="w-5 h-5 mt-0.5 flex-shrink-0" />
                        <span>
                          Download een authenticator-app zoals Google
                          Authenticator of Authy
                        </span>
                      </li>
                      <li className="flex items-start gap-3">
                        <QrCode className="w-5 h-5 mt-0.5 flex-shrink-0" />
                        <span>
                          Open je authenticator-app en scan de QR-code hierboven
                        </span>
                      </li>
                      <li className="flex items-start gap-3">
                        <KeyRound className="w-5 h-5 mt-0.5 flex-shrink-0" />
                        <span>
                          Voer de 6-cijferige code uit je app in om de
                          installatie te verifiëren
                        </span>
                      </li>
                    </ol>
                  </div>

                  <button
                    className="w-full py-3 px-4 bg-primary text-white rounded-lg hover:bg-primary-hover
                           transition-colors duration-200 font-medium inline-flex items-center justify-center gap-2"
                    onClick={() => setTotpStep("verify")}
                  >
                    Doorgaan naar verificatie
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>
              )}

              {totpStep === "verify" && (
                <div className="space-y-6">
                  <div className="text-center">
                    <p className="text-gray-600 dark:text-gray-300">
                      Voer de 6-cijferige code van je authenticator-app in
                    </p>
                  </div>

                  <div className="space-y-4">
                    <input
                      type="text"
                      className="w-full p-3 text-center text-2xl tracking-widest font-mono border
                             rounded-lg focus:ring-2 focus:ring-primary focus:border-primary
                             dark:bg-gray-700 dark:border-gray-600"
                      placeholder="000000"
                      value={verificationCode}
                      onChange={handleTOTPCodeChange}
                      maxLength={6}
                      inputMode="numeric"
                      pattern="\d*"
                      autoFocus
                    />
                    <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                      Geen code ontvangen? Controleer of de tijd op je apparaat
                      correct is
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <button
                      className="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200
                             transition-colors duration-200 font-medium dark:bg-gray-700 dark:text-gray-300
                             inline-flex items-center justify-center gap-2"
                      onClick={() => setTotpStep("initial")}
                    >
                      <ArrowLeft className="w-4 h-4" />
                      Terug naar QR-code
                    </button>
                    <button
                      className="flex-1 py-3 px-4 bg-primary text-white rounded-lg hover:bg-primary-hover
                 transition-colors duration-200 font-medium disabled:opacity-50
                 inline-flex items-center justify-center gap-2"
                      onClick={handleTOTPEnable}
                      disabled={verificationCode.length !== 6 || isVerifying}
                    >
                      {isVerifying ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            />
                          </svg>
                          Verifiëren...
                        </>
                      ) : (
                        <>
                          <Shield className="w-4 h-4" />
                          Inschakelen
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </>
          ) : (
            // Phone MFA UI for non-admin users
            <>
              {step === "phone" ? (
                <div className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label
                        htmlFor="phone"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Telefoonnummer
                      </label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                        <input
                          id="phone"
                          type="tel"
                          placeholder="+31 6 12345678"
                          value={phoneNumber}
                          onChange={(e) =>
                            setPhoneNumber(
                              formatDutchPhoneNumber(e.target.value)
                            )
                          }
                          className="w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary
                               focus:border-primary dark:bg-gray-700 dark:border-gray-600"
                        />
                      </div>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
                      <h3 className="font-medium text-lg">Hoe werkt het?</h3>
                      <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                        <li className="flex items-start gap-3">
                          <Smartphone className="w-5 h-5 mt-0.5 flex-shrink-0" />
                          <span>
                            Voer je telefoonnummer in om SMS-verificatie in te
                            stellen
                          </span>
                        </li>
                        <li className="flex items-start gap-3">
                          <MessageSquare className="w-5 h-5 mt-0.5 flex-shrink-0" />
                          <span>Je ontvangt een 6-cijferige code via SMS</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <ShieldCheck className="w-5 h-5 mt-0.5 flex-shrink-0" />
                          <span>
                            Voer de code in om twee-factor-authenticatie te
                            activeren
                          </span>
                        </li>
                      </ol>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <button
                      className="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200
                           transition-colors duration-200 font-medium dark:bg-gray-700 dark:text-gray-300"
                      onClick={onCancelled}
                    >
                      Annuleren
                    </button>
                    <button
                      className="flex-1 py-3 px-4 bg-primary text-white rounded-lg hover:bg-primary-hover
                           transition-colors duration-200 font-medium disabled:opacity-50"
                      onClick={onEnrollClicked}
                      disabled={!phoneNumber || isLoading}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Bezig...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <Send className="w-4 h-4" />
                          <span>Code versturen</span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label
                        htmlFor="code"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Verificatiecode
                      </label>
                      <div className="relative">
                        <KeyRound className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                        <input
                          id="code"
                          type="text"
                          placeholder="000000"
                          value={verifyCode}
                          onChange={(e) => setVerifyCode(e.target.value.trim())}
                          className="w-full pl-10 pr-4 py-3 text-center text-2xl tracking-widest font-mono border
                               rounded-lg focus:ring-2 focus:ring-primary focus:border-primary
                               dark:bg-gray-700 dark:border-gray-600"
                          maxLength={6}
                          inputMode="numeric"
                          pattern="\d*"
                          autoFocus
                        />
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-2">
                        Code niet ontvangen?{" "}
                        <button
                          onClick={onSendOTPClicked}
                          className="text-primary hover:text-primary-hover font-medium"
                          disabled={isLoading}
                        >
                          Verstuur opnieuw
                        </button>
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <button
                      className="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200
                           transition-colors duration-200 font-medium dark:bg-gray-700 dark:text-gray-300"
                      onClick={() => setStep("phone")}
                    >
                      <div className="flex items-center justify-center gap-2">
                        <ArrowLeft className="w-4 h-4" />
                        <span>Terug</span>
                      </div>
                    </button>
                    <button
                      className="flex-1 py-3 px-4 bg-primary text-white rounded-lg hover:bg-primary-hover
                           transition-colors duration-200 font-medium disabled:opacity-50"
                      onClick={onEnableClicked}
                      disabled={verifyCode.length !== 6 || isLoading}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Verifiëren...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <ShieldCheck className="w-4 h-4" />
                          <span>Verifiëren</span>
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};
