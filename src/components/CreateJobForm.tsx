import { useMemo, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Search } from "lucide-react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "./ui/command";
import { questionnaire } from "./werkwijze/new-jobs/questionnaire";
import { BackToDashboard } from "./BackToDashboard";

export const CreateJobForm = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [value, setValue] = useState("");

  const filteredItems = useMemo(() => {
    if (!value) return questionnaire;

    const predefinedItems = questionnaire.filter((item) =>
      item.label.toLowerCase().includes(value.toLowerCase())
    );

    // Only add custom item if input doesn't exactly match any predefined items
    const exactMatch = predefinedItems.some(
      (item) => item.label.toLowerCase() === value.toLowerCase()
    );

    if (!exactMatch && value.length > 2) {
      return [
        ...predefinedItems,
        { id: "custom", label: value, questions: [], icon: Search },
      ];
    }

    return predefinedItems;
  }, [value]);

  const preserveSearchParams = (newParams?: Record<string, string>) => {
    const currentParams = Object.fromEntries(searchParams.entries());
    const queryString = new URLSearchParams({
      ...currentParams,
      ...newParams,
    }).toString();
    return queryString ? `?${queryString}` : "";
  };

  return (
    <div className="relative">
      <div className="absolute sm:top-8 sm:left-[16%] top-4 left-6 z-10">
        <BackToDashboard />
      </div>
      <div className="w-full max-w-3xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="relative isolate sm:px-6 px-2 pt-4 lg:px-8">
          <div className="mx-auto max-w-2xl py-8 sm:py-16 lg:py-24">
            <div className="sm:text-center text-start">
              <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl font-display">
                Vind de juiste vakman voor jouw klus
              </h1>
              <p className="mt-6 sm:text-lg leading-8 text-gray-600">
                Beschrijf je klus en ontvang snel reacties van gekwalificeerde
                vakmensen in jouw buurt.
              </p>
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <div className="relative w-full max-w-lg">
                  <Command
                    filter={(value, search) => {
                      if (!search) return 1;
                      const searchValue = search.toLowerCase().trim();
                      const itemValue = value.toLowerCase().trim();
                      return itemValue.includes(searchValue) ? 1 : 0;
                    }}
                    className="rounded-lg border shadow-md"
                  >
                    <CommandInput
                      placeholder="Waar ben je naar op zoek? Bijv: lekkage verhelpen"
                      value={value}
                      onValueChange={setValue}
                      className="h-12"
                    />
                    <CommandList className="max-h-[300px] overflow-y-auto">
                      <CommandEmpty>Geen suggesties gevonden</CommandEmpty>
                      <CommandGroup heading="Populaire klussen">
                        {filteredItems.map(({ id, label, icon: Icon }, idx) => (
                          <CommandItem
                            key={id === "custom" ? "custom" : idx}
                            value={label}
                            onSelect={() => {
                              const baseUrl =
                                id === "custom"
                                  ? `/banen/new/custom`
                                  : `/banen/new/${id}`;

                              const queryParams =
                                id === "custom"
                                  ? preserveSearchParams({ title: label })
                                  : preserveSearchParams();

                              navigate(baseUrl + queryParams);
                            }}
                            className="cursor-pointer py-3 px-4 hover:bg-secondary"
                          >
                            {Icon && (
                              <Icon className="mr-2 h-4 w-4 text-primary" />
                            )}
                            {label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
