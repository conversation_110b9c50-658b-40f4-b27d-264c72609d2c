import { useState, useEffect } from "react";

import { supabase } from "@/integrations/supabase/client";
import { Message } from "@/types/chat";
import { useToast } from "./use-toast";

export const useChat = (
  chatId: string | undefined,
  jobId: string | undefined,
  currentUserId: string | undefined
) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch initial messages
  useEffect(() => {
    if (!chatId || !currentUserId) {
      setMessages([]);
      setIsLoading(false);
      return;
    }

    const fetchMessages = async () => {
      setIsLoading(true);
      setMessages([]);
      try {
        const { data, error } = await supabase
          .from("messages")
          .select(
            `
            *,
            sender:profiles!messages_sender_id_fkey(
              id,
			  company_name,
              first_name,
              last_name,
              full_name
            ),
            job:jobs!messages_job_id_fkey(
              id,
              title,
              user_id
            )
          `
          )
          .eq("chat_id", chatId)
          .order("created_at", { ascending: true });

        if (error) {
          throw error;
        }

        setMessages(
          (data as Message[]).filter(
            (item) =>
              item.receiver_id === currentUserId ||
              item.sender_id === currentUserId
          )
        );
      } catch (error) {
        console.error("Error fetching messages:", error);
        toast({
          variant: "destructive",
          title: "Fout bij ophalen berichten",
          description: "Er ging iets mis bij het ophalen van de berichten.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, [chatId, currentUserId, toast]);

  // Subscribe to new messages
  useEffect(() => {
    if (!chatId) return;

    const channel = supabase
      .channel(`messages-${chatId}`)
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "messages",
          filter: `chat_id=eq.${chatId}`,
        },
        async (payload) => {
          const newMessage = payload.new as Message;

          // Fetch additional message data
          const { data: messageData, error } = await supabase
            .from("messages")
            .select(
              `
              *,
              sender:profiles!messages_sender_id_fkey(
                id,
                first_name,
                last_name,
                full_name
              ),
              job:jobs!messages_job_id_fkey(
                id,
                title,
                user_id
              )
            `
            )
            .eq("id", newMessage.id)
            .single();

          if (error) {
            console.error("Error fetching message details:", error);
            return;
          }

          setMessages((prev) => [...prev, messageData as Message]);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [chatId]);

  // Send message function
  const sendMessage = async (content: string, receiverId: string) => {
    if (!content.trim() || !chatId || !currentUserId || !receiverId || !jobId) {
      console.error("Missing required data for sending message:", {
        content,
        chatId,
        currentUserId,
        receiverId,
      });
      return;
    }

    try {
      const {
        data: { user: sender },
      } = await supabase.auth.getUser();

      const { error } = await supabase.from("messages").insert({
        content: content.trim(),
        job_id: jobId,
        sender_id: sender.id,
        receiver_id: receiverId,
        chat_id: chatId,
      });

      if (error) {
        console.error("Error sending message:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error sending message:", error);
      toast({
        variant: "destructive",
        title: "Fout bij versturen",
        description: "Er ging iets mis bij het versturen van je bericht.",
      });
    }
  };

  return {
    messages,
    isLoading,
    sendMessage,
  };
};
