export type Profile = {
  Row: {
    balance: number | null
    btw_number: string | null
    created_at: string
    email: string | null
    first_name: string | null
    full_name: string | null
    house_number: string | null
    house_number_addition: string | null
    id: string
    kvk_number: string | null
    last_name: string | null
    phone_number: string | null
    profile_photo_url: string | null
    street_address: string | null
    updated_at: string
    user_type: string | null
  }
  Insert: {
    balance?: number | null
    btw_number?: string | null
    created_at?: string
    email?: string | null
    first_name?: string | null
    full_name?: string | null
    house_number?: string | null
    house_number_addition?: string | null
    id: string
    kvk_number?: string | null
    last_name?: string | null
    phone_number?: string | null
    profile_photo_url?: string | null
    street_address?: string | null
    updated_at?: string
    user_type?: string | null
  }
  Update: {
    balance?: number | null
    btw_number?: string | null
    created_at?: string
    email?: string | null
    first_name?: string | null
    full_name?: string | null
    house_number?: string | null
    house_number_addition?: string | null
    id?: string
    kvk_number?: string | null
    last_name?: string | null
    phone_number?: string | null
    profile_photo_url?: string | null
    street_address?: string | null
    updated_at?: string
    user_type?: string | null
  }
  Relationships: []
}