import { J<PERSON> } from './auth';

export interface JobsSchema {
  Tables: {
    jobs: {
      Row: {
        id: string;
        created_at: string;
        user_id: string;
        title: string;
        description: string;
        postal_code: string;
        house_number: string;
        house_number_addition: string | null;
        category: string;
        status: string | null;
        budget: number | null;
        response_cost: number;
        deleted_at: string | null;
        photos: J<PERSON> | null;
      };
      Insert: {
        id?: string;
        created_at?: string;
        user_id: string;
        title: string;
        description: string;
        postal_code: string;
        house_number: string;
        house_number_addition?: string | null;
        category: string;
        status?: string | null;
        budget?: number | null;
        response_cost?: number;
        deleted_at?: string | null;
        photos?: Json | null;
      };
      Update: {
        id?: string;
        created_at?: string;
        user_id?: string;
        title?: string;
        description?: string;
        postal_code?: string;
        house_number?: string;
        house_number_addition?: string | null;
        category?: string;
        status?: string | null;
        budget?: number | null;
        response_cost?: number;
        deleted_at?: string | null;
        photos?: Json | null;
      };
    };
  };
}