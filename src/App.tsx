import { useLocation } from "react-router-dom";
import { useEffect } from "react";

import { Toaster } from "@/components/ui/toaster";
import { SessionProvider } from "@/components/auth/SessionProvider";
import { ProtectedRoutes } from "@/components/routing/ProtectedRoutes";
import { ScrollToTop } from "@/components/ScrollToTop";
import { JobsProvider } from "./contexts/JobsContext";
import { initGA, trackPageView } from "./utils/analytics";

const PageTracker: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname + location.search);
  }, [location]);

  return null;
};

function App() {
  useEffect(() => {
    initGA();
  }, []);

  return (
    <>
      <PageTracker />
      <ScrollToTop />
      <SessionProvider>
        {(session) => (
          <JobsProvider>
            <ProtectedRoutes session={session} />
          </JobsProvider>
        )}
      </SessionProvider>
      <Toaster />
    </>
  );
}

export default App;
