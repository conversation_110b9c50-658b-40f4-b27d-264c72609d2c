#root {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
}

html, body {
  overflow-x: hidden;
  position: relative;
  width: 100%;
  -webkit-overflow-scrolling: touch;
  @apply bg-gradient-to-b from-background to-muted transition-colors duration-300;
  min-height: 100vh;
}

body {
  min-height: -webkit-fill-available;
}

.card {
  @apply bg-card/80 backdrop-blur-lg border border-border/30 transition-all duration-300;
}

.card:hover {
  transform: translateY(-2px);
  @apply shadow-lg shadow-primary/5;
}