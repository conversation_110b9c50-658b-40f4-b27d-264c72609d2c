// src/sanityClient.js
import sanityClient from '@sanity/client';
import imageUrlBuilder from '@sanity/image-url';

export const client = sanityClient({
  // 👇 Replace these with your project's values
  projectId: 'istrih7w', // find this in sanity.json
  dataset: 'production',      // or the name you chose
  useCdn: true, // `false` if you want to ensure fresh data
  apiVersion: '2023-05-03', // use a UTC date in YYYY-MM-DD format
});

const builder = imageUrlBuilder(client);

export function urlFor(source) {
  return builder.image(source);
}