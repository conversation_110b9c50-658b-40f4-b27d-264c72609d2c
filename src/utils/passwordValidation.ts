export const validatePassword = (password: string) => {
  const minLength = password.length >= 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const requirements = [
    { met: minLength, message: "minimaal 8 karakters" },
    { met: hasUpperCase, message: "hoofdletter" },
    { met: hasLowerCase, message: "kleine letter" },
    { met: hasNumbers, message: "cijfer" },
    { met: hasSpecialChar, message: "speciaal teken" }
  ];

  const failedRequirements = requirements
    .filter(req => !req.met)
    .map(req => req.message);

  return {
    isValid: failedRequirements.length === 0,
    errorMessage: failedRequirements.length > 0 
      ? `Wachtwoord moet bevatten: ${failedRequirements.join(", ")}`
      : ""
  };
};