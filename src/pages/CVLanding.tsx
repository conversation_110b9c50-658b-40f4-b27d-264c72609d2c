import { useState } from "react";
import { Link } from "react-router-dom";
import {
  CheckCircle,
  Phone,
  Zap,
  FileText,
  MapPin,
  Calendar,
  Info,
  AlertTriangle,
  Settings,
  RefreshCw,
  Search,
  Droplets,
} from "lucide-react";
import {
  ReactCompareSlider,
  ReactCompareSliderImage,
} from "react-compare-slider";
import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const CVLanding = () => {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>unir",
      rating: 5,
      shortText:
        "Mijn ketel viel 's avonds uit. Binnen 2 uur stond er iemand voor de deur. Probleem snel opgelost en weer warm water!",
      fullText:
        "Mijn ketel viel 's avonds uit. Binnen 2 uur stond er iemand voor de deur. Probleem snel opgelost en weer warm water! De monteur was zeer professioneel en heeft alles perfect gerepareerd. Echt een aanrader voor spoedgevallen!",
      verified: true,
    },
    {
      id: 2,
      name: "Els",
      rating: 5,
      shortText:
        "Duidelijke prijs, geen verrassingen, en mijn ketel loopt weer als nieuw. Echt een vakman die weet wat hij doet!",
      fullText:
        "Duidelijke prijs, geen verrassingen, en mijn ketel loopt weer als nieuw. Echt een vakman die weet wat hij doet! De service was uitstekend en ik ben zeer tevreden met het resultaat.",
      verified: true,
    },
    {
      id: 3,
      name: "Rick",
      rating: 5,
      shortText:
        "Na jaren geen onderhoud, nu alles weer veilig en zuinig. De monteur was vriendelijk en professioneel.",
      fullText:
        "Na jaren geen onderhoud, nu alles weer veilig en zuinig. De monteur was vriendelijk en professioneel. Hij heeft alles grondig gecontroleerd en uitgelegd wat er gedaan moest worden. Mijn ketel werkt nu perfect!",
      verified: true,
    },
    {
      id: 4,
      name: "Sandra",
      rating: 5,
      shortText:
        "Snelle service en eerlijke prijzen. De monteur legde alles duidelijk uit en gaf goede tips voor onderhoud.",
      fullText:
        "Snelle service en eerlijke prijzen. De monteur legde alles duidelijk uit en gaf goede tips voor onderhoud. Zeer tevreden met de kwaliteit van het werk en de professionele aanpak.",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={isContactModalOpen}
        setIsOpen={setIsContactModalOpen}
        jobType="cv"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-xl md:text-2xl font-bold md:tracking-wide">
              Klusgebied
            </h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setIsContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md md:text-lg md:tracking-wide"
            >
              <Phone size={18} />
              <span>Contact opnemen</span>
            </button>
            <button
              onClick={() => setIsContactModalOpen(true)}
              className="flex items-center gap-2 bg-[#40cfc1] text-white px-4 py-2 rounded-md md:text-lg md:tracking-wide"
            >
              <FileText size={18} />
              <span>Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-lg md:text-xl mb-2 md:tracking-wide md:leading-relaxed">
            CV-ketel storing, onderhoud of vervanging nodig?
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-6"></div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 max-w-4xl mx-auto md:tracking-wide md:leading-relaxed">
            Wij repareren, onderhouden en installeren CV-ketels voor een warme
            en veilige woning
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-lg md:text-xl md:tracking-wide md:leading-relaxed">
            We zijn actief in heel Nederland en kunnen vaak al dezelfde dag
            langskomen. Je krijgt 100% garantie en de zekerheid van
            gecertificeerde CV-monteurs.
          </p>
          <button
            onClick={() => setIsContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium md:text-lg md:tracking-wide"
          >
            <Phone size={18} />
            <span>Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium md:text-lg md:tracking-wide md:leading-relaxed">
                Actief in heel Nederland
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium md:text-lg md:tracking-wide md:leading-relaxed">
                Vaak dezelfde dag beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Zap className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium md:text-lg md:tracking-wide md:leading-relaxed">
                24/7 spoedservice beschikbaar
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-[#40cfc1]" size={24} />
              <h3 className="font-medium md:text-lg md:tracking-wide md:leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-anntarazevich-7251842.jpg"
            className="w-full h-full object-cover"
            alt="CV-ketel onderhoud"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 md:text-lg md:tracking-wide md:leading-relaxed">
              Onze CV-specialisten hebben ervaring met alle bekende
              CV-ketelmerken.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[500px] sm:h-[500px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-pavel-danilyuk-7937300.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-swastikarora-12098782.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
                CV-ketel storing, onderhoud of vervanging nodig?
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Een slecht functionerende CV-ketel kan leiden tot oncomfortabele
                temperaturen, hoge energiekosten en zelfs veiligheidsproblemen.
                Zonder regelmatig onderhoud kunnen storingen ontstaan die je
                comfort en veiligheid in gevaar brengen.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Met professioneel onderhoud en snelle reparaties werkt je
                CV-ketel weer zoals het hoort. We controleren alle onderdelen,
                reinigen de ketel grondig en stellen alles optimaal af voor
                maximale efficiëntie.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Een goed onderhouden CV-ketel zorgt voor constante warmte,
                lagere energiekosten en helpt veiligheidsproblemen zoals
                CO-lekkage te voorkomen.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
            CV-ketel Service
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 md:text-lg md:tracking-wide md:leading-relaxed">
            Wij lossen alle CV-problemen op, van noodgevallen tot regulier
            onderhoud. Onze diensten zijn beschikbaar in heel Nederland.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <AlertTriangle
                  size={140}
                  strokeWidth={1}
                  className="text-black"
                />
              </div>
              <h3 className="text-xl font-bold mb-4 md:text-2xl md:tracking-wide md:leading-relaxed">
                CV-ketel storing verhelpen
              </h3>
              <p className="text-gray-600 md:text-lg md:tracking-wide md:leading-relaxed">
                Geen warm water, lage druk, geen ontsteking of foutcodes? Wij
                lossen CV-storingen snel en vakkundig op, meestal binnen 1 uur.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Settings size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl font-bold mb-4 md:text-2xl md:tracking-wide md:leading-relaxed">
                Periodiek CV-ketel onderhoud
              </h3>
              <p className="text-gray-600 md:text-lg md:tracking-wide md:leading-relaxed">
                Reiniging, inspectie en afstelling van je CV-ketel. Inclusief
                CO-meting, veiligheidstest en uitgebreide rapportage.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <RefreshCw size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl font-bold mb-4 md:text-2xl md:tracking-wide md:leading-relaxed">
                CV-ketel vervangen
              </h3>
              <p className="text-gray-600 md:text-lg md:tracking-wide md:leading-relaxed">
                Is je CV-ketel verouderd of defect? Wij vervangen het met een
                energiezuinige en efficiënte oplossing voor optimaal comfort.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Search size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl font-bold mb-4 md:text-2xl md:tracking-wide md:leading-relaxed">
                CV-ketel inspecteren
              </h3>
              <p className="text-gray-600 md:text-lg md:tracking-wide md:leading-relaxed">
                Twijfel je of je CV-ketel nog goed werkt? Wij voeren een
                uitgebreide inspectie uit en controleren op defecten of
                slijtage.
              </p>
            </div>

            {/* Service Card 5 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Droplets size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-xl font-bold mb-4 md:text-2xl md:tracking-wide md:leading-relaxed">
                CV-ketel lekkage verhelpen
              </h3>
              <p className="text-gray-600 md:text-lg md:tracking-wide md:leading-relaxed">
                Lekt je CV-ketel water of zakt de druk weg? Wij sporen lekkages
                snel op en herstellen deze vakkundig.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-cottonbro-4752984.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Remeha storing opgelost"
                />
              </div>
              <p className="md:text-lg md:tracking-wide md:leading-relaxed">
                Een Remeha CV-ketel met foutcode E5 was volledig buiten werking,
                maar is na professionele reparatie weer volledig functioneel
                voor optimale warmte en comfort.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-d-ng-nhan-324384-19031774.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Intergas onderhoud"
                />
              </div>
              <p className="md:text-lg md:tracking-wide md:leading-relaxed">
                Een vervuilde Intergas CV-ketel is grondig gereinigd en
                afgesteld, waardoor de efficiëntie is verbeterd en de veiligheid
                gegarandeerd.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-heiko-ruth-53441229-7859953.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Vaillant waterlek verholpen"
                />
              </div>
              <p className="md:text-lg md:tracking-wide md:leading-relaxed">
                Een lekkende Vaillant CV-ketel met drukverlies is vakkundig
                gerepareerd door vervanging van het defecte ventiel en
                drukafstelling.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
                24/7 service – Direct hulp bij jouw CV-ketel
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Wij staan dag en nacht voor je klaar.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Of het nu gaat om een storing, defect of dringend onderhoud, we
                lossen het snel op.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Neem contact met ons op en in veel gevallen kunnen we dezelfde
                dag nog langskomen. Zo heb je snel weer een warme woning zonder
                gedoe.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Onze eigen gecertificeerde experts werken met een persoonlijke
                en doeltreffende aanpak. Geen lange wachttijden, gewoon een
                snelle en efficiënte oplossing.
              </p>
              <p className="mb-6 md:text-lg md:tracking-wide md:leading-relaxed">
                Heb je direct hulp nodig? Neem contact met ons op.
              </p>
              <button
                onClick={() => setIsContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium md:text-lg md:tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-magda-ehlers-pexels-13772355.jpg"
                  className="w-[530px] h-[353px] object-cover rounded-lg"
                  alt="24/7 CV service"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-mohammadabbasi-30319666.jpg"
                  className="w-[530px] h-[339px] object-cover rounded-lg"
                  alt="Over Klusgebied"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je CV-ketel altijd in
                topconditie is. Of het nu gaat om reparaties, onderhoud of
                vervanging, wij verbinden je met lokaal gecertificeerde
                CV-monteurs die het vakkundig en snel oplossen.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Heb je last van een slecht werkende CV-ketel of wil je een
                afspraak inplannen? Wij maken het eenvoudig en zorgen dat je
                snel wordt geholpen door onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <ReactCompareSlider
                  className="sm:w-[550px] sm:h-[550px]"
                  itemOne={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-trishik-bose-166596160-31477786.jpg" />
                  }
                  itemTwo={
                    <ReactCompareSliderImage src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-anntarazevich-7251842.jpg" />
                  }
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-8 md:text-4xl md:tracking-wide md:leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                    Erkende en gecertificeerde CV-monteurs
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                    Afspraak gegarandeerd binnen 3 dagen
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                    Snelle en vakkundige klantenservice; elke vraag is welkom
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                    Uitstekend beoordeeld door klanten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                    Mogelijkheid om 24/7 een afspraak in te plannen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold md:text-3xl md:tracking-wide md:leading-relaxed">
                  Storingsbezoek
                </h3>
                <p className="md:text-lg md:tracking-wide md:leading-relaxed">
                  CV-ketel reparatie
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-114px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Diagnose en foutanalyse
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Advies en oplossing
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Directe reparatie indien mogelijk
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Transparante prijsopgave
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1 md:text-base md:tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold md:text-5xl md:tracking-wide">
                      €85,-
                    </span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors md:text-lg md:tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold md:text-3xl md:tracking-wide md:leading-relaxed">
                  Onderhoud
                </h3>
                <p className="md:text-lg md:tracking-wide md:leading-relaxed">
                  CV-ketel onderhoud
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-114px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Volledige inspectie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Reiniging en afstelling
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      CO-meting en veiligheidstest
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Uitgebreide rapportage
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1 md:text-base md:tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold md:text-5xl md:tracking-wide">
                      €90,-
                    </span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors md:text-lg md:tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[#40cfc1] text-white p-6 text-center">
                <h3 className="text-2xl font-bold md:text-3xl md:tracking-wide md:leading-relaxed">
                  Spoedhulp
                </h3>
                <p className="md:text-lg md:tracking-wide md:leading-relaxed">
                  24/7 beschikbaar
                </p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-114px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      24/7 bereikbaar
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Binnen 4 uur ter plaatse
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="md:text-lg md:tracking-wide md:leading-relaxed">
                      Ook avonden en weekenden
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-[#40cfc1] mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center md:text-lg md:tracking-wide md:leading-relaxed">
                      Directe oplossing
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1 md:text-base md:tracking-wide">
                      vanaf
                    </span>
                    <span className="text-4xl font-bold md:text-5xl md:tracking-wide">
                      €150,-
                    </span>
                  </div>
                  <button
                    onClick={() => setIsContactModalOpen(true)}
                    className="mt-6 w-full bg-[#40cfc1] text-white py-2 px-4 rounded-md hover:bg-[#35b5a8] transition-colors md:text-lg md:tracking-wide"
                  >
                    Offerte aanvragen
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-[#40cfc1] mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Hoe vaak moet ik mijn CV-ketel laten onderhouden?"
              answer="Voor optimale prestaties adviseren wij om uw CV-ketel minimaal eens per 2 jaar te laten onderhouden. Bij intensief gebruik of oudere ketels kan jaarlijks onderhoud nodig zijn. Regelmatig onderhoud verlengt de levensduur van uw ketel en zorgt voor veilig gebruik."
            />
            <FAQItem
              question="Onderhouden jullie ook oudere ketels?"
              answer="Ja, wij onderhouden ketels tot 15 jaar oud. Bij oudere ketels adviseren we tijdig vervanging te overwegen, vooral als reparatiekosten hoog oplopen. Onze monteurs geven eerlijk advies over de staat van uw ketel."
            />
            <FAQItem
              question="Moet ik een onderhoudscontract afsluiten?"
              answer="Nee, bij Klusgebied kunt u per keer onderhoud aanvragen zonder verplichtingen. We werken met transparante prijzen en geen verborgen kosten. U bepaalt zelf wanneer u onderhoud wilt laten uitvoeren."
            />
            <FAQItem
              question="Verhelpen jullie ook spoedstoringen?"
              answer="Ja, we hebben een 24/7 spoedservice beschikbaar, ook in de avonden en weekenden. In veel gevallen kunnen we dezelfde dag nog langskomen om uw CV-storing op te lossen."
            />
            <FAQItem
              question="Zijn er verborgen kosten verbonden aan de service?"
              answer="Nee, bij Klusgebied werken we met transparante prijzen. Voordat we beginnen, krijgt u een duidelijke offerte zonder verborgen kosten. We bespreken altijd vooraf wat er precies gedaan moet worden en wat de kosten zijn."
            />
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-4 md:text-4xl md:tracking-wide md:leading-relaxed">
                Neem nu contact met ons op voor een warme en veilige woning
              </h2>
              <div className="w-20 h-1 bg-[#40cfc1] mb-6"></div>
              <p className="mb-6 md:text-lg md:tracking-wide md:leading-relaxed">
                Onze CV-experts zijn 24/7 bereikbaar en kunnen vaak dezelfde dag
                nog op de stoep staan. Klik op de onderstaande knop om contact
                op te nemen.
              </p>
              <button
                onClick={() => setIsContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-[#40cfc1] text-white px-6 py-3 rounded-md font-medium md:text-lg md:tracking-wide"
              >
                <Phone size={18} />
                <span>Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/CV/pexels-patrycja-grobelny-57039264-12034871.jpg"
                  className="w-full h-full object-cover"
                  alt="CV-expert"
                />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-sm italic md:text-base md:tracking-wide md:leading-relaxed">
            Pieter van der Berg – CV-expert in heel Nederland, geselecteerd door
            Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl font-bold md:text-2xl md:tracking-wide">
                  Klusgebied
                </h3>
              </div>
              <p className="mb-4 md:text-lg md:tracking-wide md:leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde vakmensen bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4 md:text-xl md:tracking-wide">
                Contact
              </h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium md:text-lg md:tracking-wide">
                    Klusgebied
                  </p>
                </li>
                <li>
                  <p className="md:text-lg md:tracking-wide">
                    Slotermeerlaan 58
                  </p>
                </li>
                <li>
                  <p className="md:text-lg md:tracking-wide">
                    1064 HC Amsterdam
                  </p>
                </li>
                <li>
                  <p className="md:text-lg md:tracking-wide">KVK: 93475101</p>
                </li>
                <li>
                  <p className="md:text-lg md:tracking-wide">
                    <EMAIL>
                  </p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400 md:text-base md:tracking-wide">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-[#40cfc1]">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CVLanding;
