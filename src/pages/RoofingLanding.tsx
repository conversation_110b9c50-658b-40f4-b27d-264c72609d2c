import { useState } from "react";
import {
  Phone,
  FileText,
  MapPin,
  Calendar,
  CheckCircle,
  Shield,
  Info,
  Home,
  Triangle,
  Droplets,
} from "lucide-react";
import { Link } from "react-router-dom";

import ContactUsModal from "@/components/modal/ContactUsModal";
import FAQItem from "@/components/FAQItem";
import TestimonialCarousel from "@/components/TestimonialCarousel";

const RoofingLanding = () => {
  const [contactModalOpen, setContactModalOpen] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 5,
      shortText:
        "Binnen 48 uur was mijn platte dak volledig vernieuwd. Superstrak en netjes afgewerkt. De dak<PERSON> was echt een vakman...",
      fullText:
        "Binnen 48 uur was mijn platte dak volledig vernieuwd. Superstrak en netjes afgewerkt. <PERSON> dak<PERSON> was echt een vakman en heeft alles perfect uitgevoerd. <PERSON><PERSON> tevreden met de snelle service en kwaliteit!",
      verified: true,
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      rating: 5,
      shortText:
        "We hadden een daklekkage na een storm, en Klusgebied stuurde nog dezelfde dag een dakdekker. Perfect opgelost...",
      fullText:
        "We hadden een daklekkage na een storm, en Klusgebied stuurde nog dezelfde dag een dakdekker. Perfect opgelost en de schade was snel hersteld. Uitstekende service en zeer professioneel!",
      verified: true,
    },
    {
      id: 3,
      name: "Anouk",
      rating: 5,
      shortText:
        "Scherpe offerte, duidelijk advies, en alles volgens planning uitgevoerd. Heel tevreden met het resultaat...",
      fullText:
        "Scherpe offerte, duidelijk advies, en alles volgens planning uitgevoerd. Heel tevreden met het resultaat en de professionele aanpak. De dakdekker was zeer kundig en betrouwbaar.",
      verified: true,
    },
    {
      id: 4,
      name: "Peter",
      rating: 5,
      shortText:
        "De dakdekker had alle tijd voor mijn vragen en de installatie was zo gepiept. Heel fijn dat ze alles netjes achterlieten...",
      fullText:
        "De dakdekker had alle tijd voor mijn vragen en de installatie was zo gepiept. Heel fijn dat ze alles netjes achterlieten en uitgebreide uitleg gaven over het onderhoud. Top service!",
      verified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      {/* Contact Modal */}
      <ContactUsModal
        isOpen={contactModalOpen}
        setIsOpen={setContactModalOpen}
        jobType="dakrenovatie-of-vervanging"
      />

      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 bg-white w-full z-50 shadow-sm">
        <div className="container mx-auto px-4 py-3 flex sm:flex-row flex-col gap-2 items-center justify-between">
          <Link to="/" className="flex gap-2 items-center">
            <img src="/logo.png" alt="Klusgebied Logo" className="w-12 h-12" />
            <h1 className="text-xl font-bold tracking-wide">Klusgebied</h1>
          </Link>
          <div className="flex gap-4">
            <button
              onClick={() => setContactModalOpen(true)}
              className="sm:flex items-center hidden gap-2 bg-[#14213d] text-white px-4 py-2 rounded-md"
            >
              <Phone size={18} />
              <span className="tracking-wide">Contact opnemen</span>
            </button>
            <button
              onClick={() => setContactModalOpen(true)}
              className="flex items-center gap-2 bg-primary text-white px-4 py-2 rounded-md"
            >
              <FileText size={18} />
              <span className="tracking-wide">Vrijblijvende offerte</span>
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-80 relative">
        <div className="absolute inset-0 bg-gray-800 opacity-80 z-10" />
        <div className="relative z-10 container mx-auto px-4 pb-24 md:pb-32 text-center text-white">
          <h2 className="text-xl mb-2 tracking-wide leading-relaxed">
            Is je dak toe aan vernieuwing of onderhoud?
          </h2>
          <div className="w-20 h-1 bg-primary mx-auto mb-6"></div>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 max-w-4xl mx-auto tracking-wide leading-relaxed">
            Dak laten bedekken of vervangen – Snel, veilig en vakkundig geregeld
          </h1>
          <p className="max-w-2xl mx-auto mb-8 text-xl tracking-wide leading-relaxed">
            Laat je dak professioneel bedekken of vervangen door een erkende
            dakdekker via Klusgebied. Van platte daken met bitumen tot hellende
            pannendaken of EPDM daken.
          </p>
          <button
            onClick={() => setContactModalOpen(true)}
            className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-md font-medium"
          >
            <Phone size={18} />
            <span className="tracking-wide">Contact opnemen</span>
          </button>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-16 text-white">
            <div className="flex flex-col items-center">
              <MapPin className="mb-2 text-primary" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Actief in heel Nederland
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Calendar className="mb-2 text-primary" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Binnen 72 uur een afspraak mogelijk
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <Shield className="mb-2 text-primary" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Erkende dakdekkers met garantie
              </h3>
            </div>
            <div className="flex flex-col items-center">
              <CheckCircle className="mb-2 text-primary" size={24} />
              <h3 className="font-medium text-lg tracking-wide leading-relaxed">
                Transparante tarieven zonder verrassingen
              </h3>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gray-900">
          <img
            src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-adrien-olichon-1257089-2663254.jpg"
            className="w-full h-full object-cover"
            alt="Dakdekker aan het werk"
          />
        </div>
      </section>

      {/* Partners Section */}
      <section className="py-12 bg-gray-100">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <p className="text-gray-800 mb-6 text-lg tracking-wide leading-relaxed">
              Onze dakdekkers hebben ervaring met alle bekende
              dakbedekkingsmaterialen.
            </p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-12">
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-1.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-2.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
            <div className="w-32 h-16 relative grayscale transition-all">
              <img
                src="/images/ventilation_landing/partner-3.webp"
                className="w-[120px] h-[60px] object-contain"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Problem Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-binyaminmellish-1396122.jpg"
                  className="w-full h-full object-cover rounded-lg"
                  alt="Voor en na dakvervanging"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Lekkage, verouderde dakbedekking of slechte isolatie?
              </h2>
              <div className="w-20 h-1 bg-primary mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Een slecht onderhouden dak kan leiden tot waterlekkage, schimmel
                en hoge energiekosten. Verouderde dakbedekking, losliggende
                dakpannen of slechte isolatie vragen om professionele aandacht.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Met een professionele dakvervanging of renovatie zorgen we
                ervoor dat je dak weer volledig waterdicht is en optimaal
                isoleert. We vervangen oude materialen, herstellen de
                onderconstructie en zorgen voor een duurzame oplossing.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Een goed onderhouden dak beschermt je woning tegen alle
                weersomstandigheden en helpt je besparen op energiekosten door
                betere isolatie.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Dakdekkersdiensten
          </h2>
          <div className="w-20 h-1 bg-primary mx-auto mb-8"></div>
          <p className="text-center max-w-3xl mx-auto mb-12 text-lg tracking-wide leading-relaxed">
            Wij lossen alle dakproblemen op, van noodgevallen tot regulier
            onderhoud. Onze diensten zijn beschikbaar in heel Nederland.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Service Card 1 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Home size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Platte daken bedekken
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-relaxed">
                Met bitumen of EPDM. Volledig waterdicht, duurzaam en
                onderhoudsarm. Wij zorgen voor een professionele afwerking die
                jaren meegaat.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Triangle size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Hellende daken vervangen
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-relaxed">
                Dakpannen vernieuwen, panlatten controleren en onderconstructie
                inspecteren. Inclusief isolatie en afwerking voor optimaal
                wooncomfort.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Droplets size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Lekkage opsporen en herstellen
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-relaxed">
                Spoedreparaties mogelijk, inclusief tijdelijke afdichting bij
                regen. We lokaliseren de bron en zorgen voor een duurzame
                oplossing.
              </p>
            </div>

            {/* Service Card 4 */}
            <div className="bg-white p-8 rounded-lg shadow-md flex flex-col items-center text-center">
              <div className="mb-6">
                <Shield size={140} strokeWidth={1} className="text-black" />
              </div>
              <h3 className="text-2xl font-bold mb-4 tracking-wide leading-relaxed">
                Dakisolatie aanbrengen
              </h3>
              <p className="text-gray-600 text-lg tracking-wide leading-relaxed">
                Subsidie mogelijk bij isolerende dakvernieuwing. Bespaar op
                energiekosten en verbeter het wooncomfort met professionele
                isolatie.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-binyaminmellish-186077.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Platte dakbedekking vervangen"
                />
              </div>
              <p className="text-lg tracking-wide leading-relaxed">
                Een verouderd plat dak met scheuren en lekkage is volledig
                vernieuwd met hoogwaardige EPDM dakbedekking voor jarenlange
                bescherming.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-joaojesusdesign-925684.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Hellend pannendak vervangen"
                />
              </div>
              <p className="text-lg tracking-wide leading-relaxed">
                Een hellend dak met beschadigde dakpannen en slechte isolatie is
                volledig gerenoveerd inclusief nieuwe pannen en
                isolatiemateriaal.
              </p>
            </div>
            <div className="text-center">
              <div className="relative w-full aspect-square max-w-xs mx-auto mb-4 overflow-hidden rounded-lg">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-pixabay-209266.jpg"
                  className="w-[360px] h-[300px] object-cover"
                  alt="Daklekkage spoedservice"
                />
              </div>
              <p className="text-lg tracking-wide leading-relaxed">
                Een acute daklekkage tijdens een storm is binnen 24 uur
                professioneel hersteld met tijdelijke en permanente afdichting.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 24/7 Service Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                24/7 service – Direct hulp bij dakproblemen
              </h2>
              <div className="w-20 h-1 bg-primary mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Wij staan dag en nacht voor je klaar.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Of het nu gaat om een lekkage, stormschade of dringend
                onderhoud, we lossen het snel op.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Neem contact met ons op en in veel gevallen kunnen we binnen 72
                uur langskomen. Zo heb je snel weer een waterdicht dak zonder
                gedoe.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Onze eigen gecertificeerde dakdekkers werken met een
                persoonlijke en doeltreffende aanpak. Geen lange wachttijden,
                gewoon een snelle en efficiënte oplossing.
              </p>
              <p className="mb-6 text-lg tracking-wide leading-relaxed">
                Heb je direct hulp nodig? Neem contact met ons op.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-md font-medium"
              >
                <Phone size={18} />
                <span className="tracking-wide">Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-pixabay-221525.jpg"
                  className="w-[530px] h-[353px] object-cover rounded-lg"
                  alt="24/7 dakdekker service"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Prijzen
          </h2>
          <div className="w-20 h-1 bg-primary mx-auto mb-12"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Pricing Card 1 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-primary text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">Plat dak</h3>
                <p className="tracking-wide">Bitumen of EPDM</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Oude bedekking verwijderen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Nieuwe dakbedekking
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Afwerking en afvoer
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Garantie op materiaal
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€60,-</span>
                  </div>
                  <p className="text-sm text-gray-500 tracking-wide">per m²</p>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-[#b45309] transition-colors"
                  >
                    <span className="tracking-wide">Offerte aanvragen</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 2 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-primary text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Hellend dak
                </h3>
                <p className="tracking-wide">Dakpannen</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Oude dak verwijderen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Nieuwe dakpannen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Panlatten en onderlaag
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Afwerking nokken en randen
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€90,-</span>
                  </div>
                  <p className="text-sm text-gray-500 tracking-wide">per m²</p>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-[#b45309] transition-colors"
                  >
                    <span className="tracking-wide">Offerte aanvragen</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 3 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-primary text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Lekkage herstel
                </h3>
                <p className="tracking-wide">Spoedservice</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Lekkage opsporen
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Tijdelijke afdichting
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Permanente reparatie
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      24/7 beschikbaar
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€150,-</span>
                  </div>
                  <p className="text-sm text-gray-500 tracking-wide">
                    per reparatie
                  </p>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-[#b45309] transition-colors"
                  >
                    <span className="tracking-wide">Offerte aanvragen</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Pricing Card 4 */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-primary text-white p-6 text-center">
                <h3 className="text-2xl font-bold tracking-wide">
                  Dakisolatie
                </h3>
                <p className="tracking-wide">Energiebesparing</p>
              </div>
              <div className="p-6 flex flex-col h-[calc(100%-104px)]">
                <ul className="space-y-3 mb-8">
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Isolatiemateriaal
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Binnen- of buitenzijde
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="text-base tracking-wide leading-relaxed">
                      Subsidie mogelijk
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle
                      className="text-primary mt-1 mr-3 flex-shrink-0"
                      size={18}
                    />
                    <span className="flex items-center text-base tracking-wide leading-relaxed">
                      Energiebesparing
                      <Info size={16} className="ml-1 text-gray-400" />
                    </span>
                  </li>
                </ul>
                <div className="flex-grow" />
                <div className="text-center">
                  <div className="flex items-baseline justify-center">
                    <span className="text-sm text-gray-500 mr-1">vanaf</span>
                    <span className="text-4xl font-bold">€45,-</span>
                  </div>
                  <p className="text-sm text-gray-500 tracking-wide">per m²</p>
                  <button
                    onClick={() => setContactModalOpen(true)}
                    className="mt-6 w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-[#b45309] transition-colors"
                  >
                    <span className="tracking-wide">Offerte aanvragen</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-video max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-pixabay-48882.jpg"
                  className="w-[530px] h-[339px] object-cover rounded-lg"
                  alt="Over Klusgebied dakdekkers"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Over Klusgebied
              </h2>
              <div className="w-20 h-1 bg-primary mb-6"></div>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Bij Klusgebied zorgen we ervoor dat je dak altijd in topconditie
                is. Of het nu gaat om vervanging, reparatie of onderhoud, wij
                verbinden je met lokaal gecertificeerde dakdekkers die het
                vakkundig en snel oplossen.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                We geloven in service zonder gedoe. Onze specialisten staan
                klaar om net dat extra stapje te zetten, zodat jij helemaal
                tevreden bent. Dat zie je terug in onze 4.8 uit 5
                klantbeoordeling – tevreden klanten zijn voor ons de standaard.
              </p>
              <p className="mb-4 text-lg tracking-wide leading-relaxed">
                Heb je last van een lekkend dak of wil je een afspraak
                inplannen? Wij maken het eenvoudig en zorgen dat je snel wordt
                geholpen door onze lokale expert bij jou in de buurt.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <TestimonialCarousel testimonials={testimonials} />

      {/* Benefits Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <div className="relative w-full aspect-square max-w-md mx-auto">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-pixabay-48895.jpg"
                  className="w-full h-full object-cover rounded-lg"
                  alt="Voordelen van professionele dakvervanging"
                />
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-8 tracking-wide leading-relaxed">
                Jouw voordelen
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <CheckCircle
                    className="text-primary mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Erkende dakdekkers met jarenlange ervaring
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-primary mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Afspraak gegarandeerd binnen 72 uur
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-primary mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Snelle en vakkundige klantenservice; elke vraag is welkom
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-primary mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Uitstekend beoordeeld door klanten
                  </span>
                </li>
                <li className="flex items-start">
                  <CheckCircle
                    className="text-primary mt-1 mr-3 flex-shrink-0"
                    size={20}
                  />
                  <span className="text-lg tracking-wide leading-relaxed">
                    Mogelijkheid om 24/7 een afspraak in te plannen
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-4 tracking-wide leading-relaxed">
            Veelgestelde vragen
          </h2>
          <div className="w-20 h-1 bg-primary mx-auto mb-12"></div>

          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <FAQItem
              question="Hoe weet ik of mijn dak aan vervanging toe is?"
              answer="Scheuren, lekkage, verouderde dakbedekking of losliggende onderdelen zijn duidelijke signalen. Wij doen gratis een inspectie om de staat van je dak te beoordelen en adviseren over de beste oplossing."
            />
            <FAQItem
              question="Wat is de levensduur van een nieuw dak?"
              answer="EPDM en bitumen daken gaan ca. 20-40 jaar mee. Dakpannen tot 50 jaar, mits goed geplaatst en onderhouden. De levensduur hangt af van het materiaal, de kwaliteit van de installatie en het onderhoud."
            />
            <FAQItem
              question="Werken jullie ook met spoed bij daklekkage?"
              answer="Ja, absoluut. Bij lekkage of stormschade kunnen we vaak binnen 24 uur een dakdekker sturen voor noodreparaties. We begrijpen dat dakproblemen urgent zijn en handelen daar naar."
            />
            <FAQItem
              question="Zijn er verborgen kosten verbonden aan de service?"
              answer="Nee, bij Klusgebied werken we met transparante prijzen. Voordat we beginnen, krijgt u een duidelijke offerte zonder verborgen kosten. We bespreken altijd vooraf wat er precies gedaan moet worden."
            />
            <FAQItem
              question="Heb ik recht op subsidie voor dakisolatie?"
              answer="In veel gevallen wel. Er zijn verschillende subsidieregelingen beschikbaar voor energiebesparende maatregelen zoals dakisolatie. We adviseren je hier graag over en helpen bij het aanvragen."
            />
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-12 items-center">
            <div className="md:w-1/2">
              <h2 className="text-4xl font-bold mb-4 tracking-wide leading-relaxed">
                Neem nu contact met ons op voor een waterdicht dak
              </h2>
              <div className="w-20 h-1 bg-primary mb-6"></div>
              <p className="mb-6 text-lg tracking-wide leading-relaxed">
                Onze dakdekkers zijn 24/7 bereikbaar en kunnen vaak binnen 72
                uur op de stoep staan. Klik op de onderstaande knop om contact
                op te nemen.
              </p>
              <button
                onClick={() => setContactModalOpen(true)}
                className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-md font-medium"
              >
                <Phone size={18} />
                <span className="tracking-wide">Contact opnemen</span>
              </button>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden">
                <img
                  src="https://bbyifnpcqefabwexvxdc.supabase.co/storage/v1/object/public/landing-images/Roofing/pexels-steffen-coonan-1005786-2098624.jpg"
                  className="w-full h-full object-cover"
                  alt="Dakdekker expert"
                />
              </div>
            </div>
          </div>
          <div className="text-center mt-4 text-sm italic tracking-wide leading-relaxed">
            Henk van der Berg – dakdekker-expert in heel Nederland, geselecteerd
            door Klusgebied.
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-center">
            <div>
              <div className="flex items-center mb-4 gap-2">
                <img
                  src="/logo.png"
                  alt="Klusgebied Logo"
                  className="w-12 h-12"
                />
                <h3 className="text-xl font-bold tracking-wide">Klusgebied</h3>
              </div>
              <p className="mb-4 text-base tracking-wide leading-relaxed">
                Plaats vandaag nog je klus en ontvang gratis offertes van
                zorgvuldig geselecteerde dakdekkers bij jou in de buurt! Binnen
                no-time staat een betrouwbare professional voor je klaar om de
                klus vakkundig uit te voeren. Laat het werk met een gerust hart
                uit handen nemen.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4 tracking-wide">Contact</h3>
              <ul className="space-y-2">
                <li>
                  <p className="font-medium tracking-wide">Klusgebied</p>
                </li>
                <li>
                  <p className="tracking-wide">Slotermeerlaan 58</p>
                </li>
                <li>
                  <p className="tracking-wide">1064 HC Amsterdam</p>
                </li>
                <li>
                  <p className="tracking-wide">KVK: 93475101</p>
                </li>
                <li>
                  <p className="tracking-wide"><EMAIL></p>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            <p>
              &copy; 2025 - Alle rechten voorbehouden -{" "}
              <a href="#" className="hover:text-primary">
                Privacyverklaring
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default RoofingLanding;
