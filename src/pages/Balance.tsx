import { useState, useEffect } from "react";
import { AlertTriangle } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Transaction } from "@/types/database";
import { BackToDashboard } from "@/components/BackToDashboard";
import { BalanceCard } from "@/components/balance/BalanceCard";
import { TransactionHistory } from "@/components/balance/TransactionHistory";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/components/auth/hooks/useAuth";
import { BalanceSkeleton } from "./BalanceSkeleton";

export const Balance = () => {
  const { userProfile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [amount, setAmount] = useState("");
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isAddingFunds, setIsAddingFunds] = useState(false);
  const { toast } = useToast();

  const fetchBalance = async () => {
    try {
      const { data: transactionsData, error: transactionsError } =
        await supabase
          .from("balance_transactions")
          .select("*")
          .eq("user_id", userProfile.id)
          .order("created_at", { ascending: false });

      if (transactionsError) {
        throw transactionsError;
      }

      if (transactionsData) {
        setTransactions(transactionsData as Transaction[]);
      }
    } catch (error) {
      console.error("Error fetching balance:", error);
      toast({
        title: "Fout",
        description: "Er ging iets mis bij het ophalen van je saldo.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBalance();
  }, []);

  if (loading) {
    return <BalanceSkeleton />;
  }

  return (
    <div className="p-6 space-y-6 overflow-y-auto h-[calc(100vh-6rem)]">
      <BackToDashboard />

      {userProfile.balance !== null && userProfile.balance < 20 && (
        <Alert
          variant="default"
          className="mb-6 border-yellow-500 bg-yellow-50"
        >
          <AlertTriangle className="h-4 w-4 text-yellow-500" />
          <AlertDescription className="text-yellow-700">
            Je saldo is lager dan €20. Vul je saldo aan om op klussen te kunnen
            reageren.
          </AlertDescription>
        </Alert>
      )}

      <h2 className="text-3xl font-bold">Mijn Saldo</h2>

      <div className="space-y-6">
        <BalanceCard
          balance={userProfile.balance}
          amount={amount}
          setAmount={setAmount}
          isAddingFunds={isAddingFunds}
        />
        <TransactionHistory transactions={transactions} />
      </div>
    </div>
  );
};

export default Balance;
