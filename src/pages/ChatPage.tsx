import { useEffect, useMemo, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Loader2, ArrowLeft } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useChat } from "@/hooks/useChat";
import { BackToDashboard } from "@/components/BackToDashboard";
import { ChatContainer } from "@/components/chat/ChatContainer";
import { ChatList } from "@/components/chat/ChatList";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";
import { useAuth } from "@/components/auth/hooks/useAuth";

const ChatPage = () => {
  const { userProfile } = useAuth();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const chatId = searchParams.get("chatId");

  const [currentUser, setCurrentUser] = useState<any>(null);
  const [selectedChat, setSelectedChat] = useState<any>(null);
  const { toast } = useToast();
  const [isLoadingUser, setIsLoadingUser] = useState(true);
  const isMobile = useIsMobile();

  const currentUserId = useMemo(() => userProfile?.id, [userProfile?.id]);

  const { data: chats, isLoading } = useQuery({
    queryKey: ["chats", currentUserId],
    queryFn: async () => {
      // First check if user is vakman
      const { data: profile } = await supabase
        .from("profiles")
        .select("user_type")
        .eq("id", currentUserId)
        .single();

      if (profile?.user_type === "vakman") {
        const { data: responses, error } = await supabase
          .from("chats")
          .select(
            `
            *,
            jobs!inner (
              id,
              title,
              user_id,
              created_at,
              status
            ),
            profiles!chats_craftman_id_fkey (
              id,
              first_name,
              last_name,
              full_name,
              profile_photo_url,
              company_name
            )
          `
          )
          .eq("craftman_id", currentUserId)
          .neq("status", "pending");

        if (error) {
          console.error("Error fetching responses:", error);
          return [];
        }

        return responses || [];
      } else {
        // For klusaanvrager, first get their jobs
        const { data: responses, error } = await supabase
          .from("chats")
          .select(
            `
            *,
            jobs!inner (
              id,
              title,
              user_id,
              created_at,
              status
            ),
            profiles!chats_craftman_id_fkey (
              id,
              first_name,
              last_name,
              full_name,
              profile_photo_url,
              company_name
            )
          `
          )
          .eq("client_id", currentUserId)
          .neq("status", "pending");

        if (error) {
          console.error("Error fetching responses:", error);
          return [];
        }

        return responses || [];
      }
    },
    staleTime: 1000 * 60,
    gcTime: 1000 * 60 * 5,
    refetchInterval: 1000 * 60 * 5,
  });

  useEffect(() => {
    const fetchJobAndUser = async () => {
      try {
        const {
          data: { user },
          error: userError,
        } = await supabase.auth.getUser();

        if (userError) throw userError;

        if (!user) {
          navigate("/");
          return;
        }

        const { data: profile, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", user.id)
          .single();

        if (profileError) throw profileError;

        setCurrentUser(profile);
        setIsLoadingUser(false);
      } catch (error) {
        console.error("Error fetching user details:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description:
            "Er is een fout opgetreden bij het laden van je gegevens.",
        });
        setIsLoadingUser(false);
      }
    };

    fetchJobAndUser();
  }, [navigate, toast]);

  useEffect(() => {
    // Early return if no chatId or chats are not loaded
    if (!chatId || !chats) return;

    // Find chat with matching slug and update selected chat if found
    const selectedChatData = chats.find((chat) => chat.slug === chatId);
    if (selectedChatData) {
      setSelectedChat(selectedChatData);
    }
  }, [chatId, chats]);

  const {
    messages,
    isLoading: isLoadingMessages,
    sendMessage,
  } = useChat(
    selectedChat?.slug,
    selectedChat?.jobs?.id,
    selectedChat?.profiles?.id
  );

  const handleSendMessage = (content: string) => {
    if (selectedChat?.jobs?.id && selectedChat?.profiles?.id) {
      sendMessage(content, selectedChat.profiles.id);
    }
  };

  const handleBackToList = () => {
    setSelectedChat(null);
  };

  if (isLoadingUser) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-85px)]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const showChatList = !isMobile || (isMobile && !selectedChat);
  const showChatContainer = !isMobile || (isMobile && selectedChat);
  const showBackToDashboard = !isMobile || (isMobile && !selectedChat);

  return (
    <div className="w-full px-0">
      <div className="max-w-[2000px] mx-auto sm:space-y-6 space-y-3 p-6">
        {showBackToDashboard && (
          <div className="mb-2">
            <BackToDashboard />
          </div>
        )}
        <div className="sm:mt-2 mt-1">
          <div className="grid grid-cols-1 md:grid-cols-4 sm:gap-6 gap-3">
            {showChatList && (
              <div className="space-y-4">
                {currentUser && (
                  <ChatList
                    onSelectChat={setSelectedChat}
                    activeId={selectedChat?.id}
                    chats={chats}
                    isLoading={isLoading}
                  />
                )}
              </div>
            )}
            {showChatContainer && (
              <div className={`md:col-span-3 ${isMobile ? "col-span-1" : ""}`}>
                {isMobile && (
                  <Button
                    variant="ghost"
                    className="mb-2"
                    onClick={handleBackToList}
                  >
                    <ArrowLeft className="h-2 w-4 mr-2" />
                    Terug naar chats
                  </Button>
                )}
                <ChatContainer
                  messages={messages}
                  activeChat={selectedChat}
                  currentUser={currentUser}
                  onSendMessage={handleSendMessage}
                  isLoading={isLoadingMessages}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatPage;
