import { Loader2 } from "lucide-react";
import { useCallback, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";

import { PasswordStep } from "@/components/auth/PasswordStep";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export default function ResetPasswordPage() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    formState: { errors },
    handleSubmit,
    setError,
  } = useForm();

  const handleSessionError = useCallback(() => {
    localStorage.clear();
    navigate("/auth");
    toast({
      variant: "destructive",
      title: "Sessie verlopen",
      description: "Je sessie is verlopen. Log opnieuw in.",
    });
  }, [navigate, toast]);

  const handleUpdatePassword = useCallback(
    async ({
      password,
      confirmPassword,
    }: {
      password: string;
      confirmPassword: string;
    }) => {
      try {
        // Validate password match
        if (password !== confirmPassword) {
          setError("confirmPassword", {
            message:
              "De wachtwoorden komen niet overeen. Controleer of je twee keer hetzelfde wachtwoord hebt ingevuld.",
          });
          return;
        }

        // Get current session
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError || !session) {
          console.error("Session error:", sessionError);
          handleSessionError();
          return;
        }

        // Update password
        setIsLoading(true);
        const { error: updateError } = await supabase.auth.updateUser({
          password,
        });

        if (updateError) {
          setError("confirmPassword", { message: updateError.message });
          toast({
            variant: "destructive",
            title: "Fout",
            description: updateError.message,
          });
          return;
        }

        // Success handling
        toast({
          variant: "default",
          title: "Succes",
          description: "Uw wachtwoord is succesvol gewijzigd",
        });

        // Cleanup and redirect
        await supabase.auth.signOut();
        navigate("/");
      } catch (error) {
        console.error("Password update failed:", error);
        toast({
          variant: "destructive",
          title: "Fout",
          description: "Er is een onverwachte fout opgetreden",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [handleSessionError, navigate, setError, toast]
  );

  return (
    <div className="bg-gradient-to-b from-background px-6 mb-8 via-muted/30 to-background min-h-96 flex items-center justify-center">
      <Card className="shadow-2xl bg-white/95 backdrop-blur p-8 rounded-2xl w-96 border-t border-white/20 animate-fade-in flex flex-col gap-4 mt-32">
        <h1>Wachtwoord opnieuw instellen</h1>

        <form
          onSubmit={handleSubmit(handleUpdatePassword)}
          className="space-y-4"
        >
          <PasswordStep
            register={register}
            errors={errors}
            onBack={() => {}}
            view={"reset"}
          />

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Reset Password
          </Button>
        </form>
      </Card>
    </div>
  );
}
