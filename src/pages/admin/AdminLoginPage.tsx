import { useState } from "react";
import { <PERSON>, EyeOff, Lock, User } from "lucide-react";
import { useForm } from "react-hook-form";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface AdminLoginFormData {
  email: string;
  password: string;
}

export const AdminLoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<AdminLoginFormData>();

  const onSubmit = async (data: AdminLoginFormData) => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (error) {
        setError("password", { message: "Ongeldige inloggegevens" });
        toast({
          variant: "destructive",
          title: "Inloggen mislukt",
          description: "Controleer uw e-mail en wachtwoord.",
        });
        return;
      }
    } catch (e) {
      toast({
        variant: "destructive",
        title: "Fout",
        description: "Er is een onverwachte fout opgetreden.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-200 dark:from-gray-900 dark:to-gray-800">
      <Card className="w-full max-w-md shadow-xl border-0 animate-fade-in">
        <CardContent className="p-8">
          <div className="mb-8 text-center">
            <img
              src="/logo.png"
              alt="Klus Admin"
              className="mx-auto h-14 w-14 mb-2"
            />
            <h1 className="text-2xl font-bold text-primary mb-1">
              Admin Login
            </h1>
            <p className="text-muted-foreground text-sm">
              Log in met uw beheerdersaccount
            </p>
          </div>
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <Label htmlFor="email" className="mb-1 flex items-center gap-2">
                <User className="h-4 w-4" />
                E-mailadres
              </Label>
              <Input
                id="email"
                type="email"
                autoComplete="email"
                placeholder="<EMAIL>"
                {...register("email", {
                  required: "E-mailadres is verplicht",
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: "Ongeldig e-mailadres",
                  },
                })}
                className={errors.email ? "border-red-500" : ""}
                disabled={isLoading}
              />
              {errors.email && (
                <p className="text-xs text-red-500 mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>
            <div>
              <Label
                htmlFor="password"
                className="mb-1 flex items-center gap-2"
              >
                <Lock className="h-4 w-4" />
                Wachtwoord
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  placeholder="Uw wachtwoord"
                  {...register("password", {
                    required: "Wachtwoord is verplicht",
                  })}
                  className={errors.password ? "border-red-500 pr-10" : "pr-10"}
                  disabled={isLoading}
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-2 text-gray-400 hover:text-primary"
                  onClick={() => setShowPassword((v) => !v)}
                  aria-label={
                    showPassword ? "Verberg wachtwoord" : "Toon wachtwoord"
                  }
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-xs text-red-500 mt-1">
                  {errors.password.message}
                </p>
              )}
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
              size="lg"
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <span className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
                  Inloggen...
                </span>
              ) : (
                "Inloggen"
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
