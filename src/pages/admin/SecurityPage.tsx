import { Shield, Key, Lock, UserCog } from "lucide-react";

import { Card } from "@/components/ui/card";

const SecurityPage = () => {
  return (
    <div className="max-w-7xl px-6 sm:px-0 mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Beveiligingsinstellingen</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Shield className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Toegangscontrole</p>
              <p className="text-sm text-muted-foreground">
                Beheer toegangsrechten en rollen voor verschillende gebruikers
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Key className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">API Sleutels</p>
              <p className="text-sm text-muted-foreground">
                Beheer en monitor API sleutels en toegang
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Lock className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Authenticatie Instellingen</p>
              <p className="text-sm text-muted-foreground">
                Configureer authenticatie methoden en vereisten
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <UserCog className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-semibold mb-2">Gebruikersbeheer</p>
              <p className="text-sm text-muted-foreground">
                Beheer gebruikersaccounts en machtigingen
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default SecurityPage;
