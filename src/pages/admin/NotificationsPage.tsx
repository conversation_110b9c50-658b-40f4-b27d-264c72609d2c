import { Bell, Settings, Users } from "lucide-react";

import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";

const NotificationsPage = () => {
  type NotificationSettings = {
    new_user: boolean;
    system_alert: boolean;
    email: boolean;
  };
  const [settings, setSettings] = useState<NotificationSettings>({
    new_user: false,
    system_alert: false,
    email: false,
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const { data, error } = await supabase
        .from("admin_settings")
        .select("value")
        .eq("key", "notification")
        .single();
      if (error) throw error;

      if (data?.value) {
        setSettings(data.value as NotificationSettings);
      }
    } catch (error) {
      console.error("Error fetching settings:", error);
    }
  };

  const updateSettings = async (
    key: keyof NotificationSettings,
    value: boolean
  ) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);

      const { error } = await supabase.from("admin_settings").upsert(
        {
          key: "notification",
          value: newSettings,
        },
        { onConflict: "key" }
      );

      if (error) throw error;
    } catch (error) {
      console.error("Error updating settings:", error);
      // Revert settings if update fails
      setSettings(settings);
    }
  };

  return (
    <div className="max-w-7xl mx-auto sm:px-0 px-6 py-6">
      <h1 className="text-2xl font-bold mb-6">Notificatie Instellingen</h1>
      <div className="space-y-4">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Users className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-semibold">Nieuwe Gebruiker Meldingen</p>
                <p className="text-sm text-muted-foreground">
                  Ontvang meldingen bij nieuwe registraties
                </p>
              </div>
            </div>
            <Switch
              checked={settings.new_user}
              onCheckedChange={(checked) => updateSettings("new_user", checked)}
            />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Bell className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-semibold">Systeem Alerts</p>
                <p className="text-sm text-muted-foreground">
                  Belangrijke systeem notificaties
                </p>
              </div>
            </div>
            <Switch
              checked={settings.system_alert}
              onCheckedChange={(checked) =>
                updateSettings("system_alert", checked)
              }
            />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Settings className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-semibold">Email Notificaties</p>
                <p className="text-sm text-muted-foreground">
                  Beheer email notificatie instellingen
                </p>
              </div>
            </div>
            <Switch
              checked={settings.email}
              onCheckedChange={(checked) => updateSettings("email", checked)}
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default NotificationsPage;
