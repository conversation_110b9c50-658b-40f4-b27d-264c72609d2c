import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, DollarSign } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { addDays } from "date-fns";
import { useState } from "react";
import { DateRange } from "react-day-picker";

import { supabase } from "@/integrations/supabase/client";
import { BackToDashboard } from "@/components/BackToDashboard";
import { AnalyticsHeader } from "@/components/admin/analytics/AnalyticsHeader";
import { StatCard } from "@/components/admin/analytics/StatCard";
import { UserDistributionChart } from "@/components/admin/analytics/UserDistributionChart";

const AnalyticsPage = () => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  });

  // Query voor actieve gebruikers binnen de geselecteerde periode
  const { data: activeUsers } = useQuery({
    queryKey: ["activeUsers", dateRange],
    queryFn: async () => {
      let query = supabase
        .from("profiles")
        .select("*", { count: "exact", head: true });

      if (dateRange?.from) {
        query = query.gte("created_at", dateRange.from.toISOString());
      }
      if (dateRange?.to) {
        query = query.lte("created_at", dateRange.to.toISOString());
      }

      const { count } = await query;
      return count || 0;
    },
  });

  // Query voor nieuwe klussen binnen de geselecteerde periode
  const { data: newJobs } = useQuery({
    queryKey: ["newJobs", dateRange],
    queryFn: async () => {
      let query = supabase
        .from("jobs")
        .select("*", { count: "exact", head: true });

      if (dateRange?.from) {
        query = query.gte("created_at", dateRange.from.toISOString());
      }
      if (dateRange?.to) {
        query = query.lte("created_at", dateRange.to.toISOString());
      }

      const { count } = await query;
      return count || 0;
    },
  });

  // Query voor voltooide klussen percentage binnen de geselecteerde periode
  const { data: completionRate } = useQuery({
    queryKey: ["completionRate", dateRange],
    queryFn: async () => {
      let baseQuery = supabase.from("jobs").select("*", { count: "exact" });

      if (dateRange?.from) {
        baseQuery = baseQuery.gte("created_at", dateRange.from.toISOString());
      }
      if (dateRange?.to) {
        baseQuery = baseQuery.lte("created_at", dateRange.to.toISOString());
      }

      const { count: totalCount } = await baseQuery;
      const { count: completedCount } = await baseQuery.eq(
        "status",
        "completed"
      );

      if (!totalCount) return 0;
      return Math.round(((completedCount || 0) / totalCount) * 100);
    },
  });

  // Query voor platform omzet binnen de geselecteerde periode
  const { data: platformRevenue } = useQuery({
    queryKey: ["platformRevenue", dateRange],
    queryFn: async () => {
      let query = supabase
        .from("balance_transactions")
        .select("amount")
        .eq("type", "withdrawal");

      if (dateRange?.from) {
        query = query.gte("created_at", dateRange.from.toISOString());
      }
      if (dateRange?.to) {
        query = query.lte("created_at", dateRange.to.toISOString());
      }

      const { data } = await query;
      return (
        data?.reduce(
          (sum, transaction) => sum + Number(transaction.amount),
          0
        ) || 0
      );
    },
  });

  return (
    <div className="min-h-[calc(100vh-85px)] bg-white">
      <div className="max-w-7xl mx-auto px-6 sm:px-0 py-6">
        <BackToDashboard />
        <AnalyticsHeader dateRange={dateRange} onDateChange={setDateRange} />

        {/* KPI Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <StatCard
            title="Actieve Gebruikers"
            value={activeUsers?.toLocaleString() || "..."}
            icon={Users}
            color="primary"
          />
          <StatCard
            title="Nieuwe Klussen"
            value={newJobs?.toLocaleString() || "..."}
            icon={TrendingUp}
            color="green"
          />
          <StatCard
            title="Voltooide Klussen"
            value={completionRate ? `${completionRate}%` : "..."}
            icon={BarChart3}
            color="blue"
          />
          <StatCard
            title="Platform Omzet"
            value={
              platformRevenue?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                style: "currency",
                currency: "EUR",
              }) || "..."
            }
            icon={DollarSign}
            color="yellow"
          />
        </div>

        {/* Gebruikers Verdeling Grafiek */}
        <UserDistributionChart dateRange={dateRange} />
      </div>
    </div>
  );
};

export default AnalyticsPage;
