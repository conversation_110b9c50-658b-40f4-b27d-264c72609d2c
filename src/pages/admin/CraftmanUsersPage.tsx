import { useState, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Users, UserPlus, UserX, Search } from "lucide-react";
import { useAtom } from "jotai";

import { Card } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { UserStatsCard } from "@/components/admin/users/UserStatsCard";
import { UsersTable } from "@/components/admin/users/UsersTable";
import { BackToDashboard } from "@/components/BackToDashboard";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import UserDetail from "@/components/admin/users/UserDetail";
import { adminUsersAtom } from "@/states/admin";
import { BulkDiplomaApproval } from "@/components/admin/users/BulkDiplomaApproval";

const createUserSchema = z.object({
  email: z.string().email("Vul een geldig e-mailadres in"),
  first_name: z.string().min(2, "Voornaam moet minimaal 2 karakters bevatten"),
  last_name: z.string().min(2, "Achternaam moet minimaal 2 karakters bevatten"),
  user_type: z.enum(["vakman", "klusaanvrager", "admin"]),
});

const CraftmanUsersPage = () => {
  const [users, setUsers] = useAtom(adminUsersAtom);

  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [activeFilter, setActiveFilter] = useState<
    | "all"
    | "new_user"
    | "craftman"
    | "job_placer"
    | "inactive"
    | "pending_diplomas"
  >("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const { toast } = useToast();
  const [stats, setStats] = useState({
    total: 0,
    new_user: 0,
    craftman: 0,
    job_placer: 0,
    inactive: 0,
    pending_diplomas: 0,
  });

  const form = useForm<z.infer<typeof createUserSchema>>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      email: "",
      first_name: "",
      last_name: "",
      user_type: "vakman",
    },
  });

  const onSubmit = async (values: z.infer<typeof createUserSchema>) => {
    try {
      const { data: session } = await supabase.auth.getSession();
      if (!session?.session?.access_token) {
        throw new Error("No session found");
      }

      const response = await fetch(
        `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/create-admin-user`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.session.access_token}`,
          },
          body: JSON.stringify(values),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create user");
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      toast({
        title: "Gebruiker aangemaakt",
        description:
          "De gebruiker is succesvol aangemaakt en ontvangt een e-mail om het wachtwoord in te stellen.",
      });

      setIsCreateDialogOpen(false);
      form.reset();
      fetchUsers(); // Refresh the users list
    } catch (error) {
      console.error("Error creating user:", error);
      toast({
        title: "Fout bij aanmaken gebruiker",
        description:
          error.message ||
          "Er is iets misgegaan bij het aanmaken van de gebruiker. Probeer het opnieuw.",
        variant: "destructive",
      });
    }
  };

  const fetchUsers = async () => {
    try {
      const { data: profiles, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("user_type", "vakman")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching users:", error);
        return;
      }

      setUsers(profiles);
    } catch (error) {
      console.error("Error in fetchUsers:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    setStats({
      total: users.length,
      new_user: users.filter((user) => user.status === "in_review").length,
      craftman: users.filter((user) => user.user_type === "vakman").length,
      job_placer: users.filter((user) => user.user_type === "klusaanvrager")
        .length,
      inactive: users.filter((user) => user.status === "inactive").length,
      pending_diplomas: users.filter((user) =>
        user.diplomas?.some(
          (diploma: { status: string }) =>
            diploma.status === "pending" || diploma.status === "in_review"
        )
      ).length,
    });
  }, [users]);

  const removeUser = (userId: string) => {
    setUsers((users) => users.filter((user) => user?.id !== userId));
  };

  const filteredUsers = useMemo(() => {
    const searchLower = searchQuery.toLowerCase();

    const matchesSearchCriteria = (user: any) => {
      const searchableFields = [
        user.email,
        user.first_name,
        user.last_name,
        user.user_type,
        user.phone,
        user.company_name,
        user.kvk_number,
        user.btw_number,
        user.balance?.toString(),
        `${user.first_name} ${user.last_name}`,
      ];

      const basicMatch = searchableFields.some((field) =>
        field?.toLowerCase().includes(searchLower)
      );

      const servicesMatch = user.services?.some((service: string) =>
        service.toLowerCase().includes(searchLower)
      );

      const diplomasMatch = user.diplomas?.some((diploma: { name: string }) =>
        diploma.name?.toLowerCase().includes(searchLower)
      );

      return basicMatch || servicesMatch || diplomasMatch;
    };

    const filterByUserStatus = (user: any) => {
      const filterConditions = {
        new_user: user.status === "in_review",
        craftman: user.user_type === "vakman",
        job_placer: user.user_type === "klusaanvrager",
        inactive: user.status === "inactive",
        pending_diplomas: user.diplomas?.some(
          (diploma: { status: string }) =>
            diploma.status === "pending" || diploma.status === "in_review"
        ),
        all: true,
      };

      return filterConditions[activeFilter];
    };

    return users.filter(
      (user) => matchesSearchCriteria(user) && filterByUserStatus(user)
    );
  }, [users, searchQuery, activeFilter]);

  const getPendingDiplomas = () => {
    return users.flatMap((user) =>
      (user.diplomas || [])
        .filter((diploma) => diploma.status === "pending")
        .map((diploma) => ({
          ...diploma,
          user_name: `${user.first_name} ${user.last_name}`,
          user_id: user.id,
        }))
    );
  };

  const updateUserDiplomaStatus = (
    userId: string,
    diplomaId: string,
    status: "approved" | "rejected"
  ) => {
    setUsers((prevUsers) =>
      prevUsers.map((user) => {
        if (user.id === userId) {
          return {
            ...user,
            diplomas: user.diplomas.map((diploma: any) =>
              diploma.id === diplomaId ? { ...diploma, status } : diploma
            ),
          };
        }
        return user;
      })
    );
  };

  const updateDiplomaStatusInSupabase = async (
    userId: string,
    updatedDiplomas: any[]
  ) => {
    const { error } = await supabase
      .from("profiles")
      .update({ diplomas: updatedDiplomas })
      .eq("id", userId);

    if (error) throw error;
  };

  const handleBulkApprove = async (documentIds: string[]) => {
    try {
      // Group documents by user_id for batch processing
      const userDocuments = documentIds.reduce(
        (acc: { [key: string]: Array<{ id: string }> }, docId) => {
          const document = getPendingDiplomas().find((doc) => doc.id === docId);
          if (document) {
            if (!acc[document.user_id]) {
              acc[document.user_id] = [];
            }
            acc[document.user_id].push(document);
          }
          return acc;
        },
        {}
      );

      // Update documents for each user
      for (const [userId, documents] of Object.entries(userDocuments)) {
        const user = users.find((u) => u.id === userId);
        if (!user?.diplomas) continue;

        const updatedDiplomas = user.diplomas.map((diploma: any) => {
          if (documents.some((doc) => doc.id === diploma.id)) {
            return { ...diploma, status: "approved" };
          }
          return diploma;
        });

        await updateDiplomaStatusInSupabase(userId, updatedDiplomas);
        updateUserDiplomaStatus(userId, documents[0].id, "approved");
      }

      toast({
        title: "Diploma's goedgekeurd",
        description: "De geselecteerde diploma's zijn succesvol goedgekeurd.",
      });
    } catch (error) {
      console.error("Error updating diploma statuses:", error);
      toast({
        title: "Fout",
        description:
          "Er is een fout opgetreden bij het bijwerken van de diploma's.",
        variant: "destructive",
      });
    }
  };

  const handleBulkReject = async (documentIds: string[]) => {
    try {
      // Group documents by user_id for batch processing
      const userDocuments = documentIds.reduce<{
        [key: string]: Array<{ id: string; user_id: string }>;
      }>((acc, docId) => {
        const document = getPendingDiplomas().find((doc) => doc.id === docId);
        if (document) {
          if (!acc[document.user_id]) {
            acc[document.user_id] = [];
          }
          acc[document.user_id].push(document);
        }
        return acc;
      }, {});

      // Update documents for each user
      for (const [userId, documents] of Object.entries(userDocuments)) {
        const user = users.find((u) => u.id === userId);
        if (!user?.diplomas) continue;

        const updatedDiplomas = user.diplomas.map((diploma: any) => {
          if (documents.some((doc) => doc.id === diploma.id)) {
            return { ...diploma, status: "rejected" };
          }
          return diploma;
        });

        await updateDiplomaStatusInSupabase(userId, updatedDiplomas);
        updateUserDiplomaStatus(userId, documents[0].id, "rejected");
      }

      toast({
        title: "Diploma's afgewezen",
        description: "De geselecteerde diploma's zijn succesvol afgewezen.",
      });
    } catch (error) {
      console.error("Error updating diploma statuses:", error);
      toast({
        title: "Fout",
        description:
          "Er is een fout opgetreden bij het bijwerken van de diploma's.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="overflow-y-auto h-[calc(100vh-85px)]">
      <div className="max-w-7xl px-6 sm:px-2 mx-auto py-6">
        <div className="sm:flex items-center justify-between mb-6">
          <BackToDashboard />
          <Button className="mb-4" onClick={() => setIsCreateDialogOpen(true)}>
            <UserPlus className="w-4 h-4 mr-2" />
            Nieuwe Gebruiker
          </Button>
        </div>

        <h1 className="text-2xl font-bold mb-6">Gebruikersbeheer</h1>

        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Zoek op naam, email, bedrijfsgegevens of documenten..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <UserStatsCard
            icon={Users}
            title="Totaal Gebruikers"
            value={stats.total}
            loading={loading}
            onClick={() => setActiveFilter("all")}
            active={activeFilter === "all"}
          />
          <UserStatsCard
            icon={UserPlus}
            title="Nieuwe Gebruiker"
            value={stats.new_user}
            loading={loading}
            iconClassName="text-green-600"
            iconBgClassName="bg-green-100"
            onClick={() => setActiveFilter("new_user")}
            active={activeFilter === "new_user"}
          />
          <UserStatsCard
            icon={UserX}
            title="Inactieve Gebruikers"
            value={stats.inactive}
            loading={loading}
            iconClassName="text-red-600"
            iconBgClassName="bg-red-100"
            onClick={() => setActiveFilter("inactive")}
            active={activeFilter === "inactive"}
          />
          <UserStatsCard
            icon={Users}
            title="Openstaande Diploma's"
            value={stats.pending_diplomas}
            loading={loading}
            iconClassName="text-yellow-600"
            iconBgClassName="bg-yellow-100"
            onClick={() => setActiveFilter("pending_diplomas")}
            active={activeFilter === "pending_diplomas"}
          />
        </div>

        {activeFilter === "pending_diplomas" && (
          <BulkDiplomaApproval
            documents={getPendingDiplomas()}
            onApprove={handleBulkApprove}
            onReject={handleBulkReject}
          />
        )}

        <Card className="mt-6">
          <UsersTable
            users={filteredUsers}
            loading={loading}
            onUserClick={setSelectedUser}
            removeUser={removeUser}
          />
        </Card>

        <Dialog
          open={!!selectedUser}
          onOpenChange={() => setSelectedUser(null)}
        >
          <DialogContent className="sm:max-w-4xl w-[calc(100%-30px)] sm:px-6 px-0 h-[calc(100%-60px)] pt-10">
            {selectedUser && <UserDetail userInfo={selectedUser} />}
          </DialogContent>
        </Dialog>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Nieuwe Gebruiker Aanmaken</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Voornaam</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Jan" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Achternaam</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Jansen" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="user_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type Gebruiker</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecteer type gebruiker" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="vakman">Vakman</SelectItem>
                          <SelectItem value="klusaanvrager">
                            Klusaanvrager
                          </SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    Annuleren
                  </Button>
                  <Button type="submit">Gebruiker Aanmaken</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default CraftmanUsersPage;
