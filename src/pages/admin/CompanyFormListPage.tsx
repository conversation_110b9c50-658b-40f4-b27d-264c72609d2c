import { useEffect, useState } from "react";
import { format } from "date-fns";
import { nl } from "date-fns/locale";
import { Building2, Calendar, Users } from "lucide-react";

import { supabase } from "@/integrations/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface CompanyForm {
  id: string;
  created_at: string;
  name: string;
  company_name: string;
  personnel_type: string;
  kvk_number: string;
  personnel_count: string;
  status: string;
}

function TableRowSkeleton() {
  return (
    <TableRow className="hover:bg-gray-50/50 transition-colors">
      {[...Array(7)].map(
        (
          _,
          index // Updated to 7 columns
        ) => (
          <TableCell key={index} className="py-4">
            <Skeleton className="h-6 w-full" />
          </TableCell>
        )
      )}
    </TableRow>
  );
}

export function CompanyFormsList() {
  const [forms, setForms] = useState<CompanyForm[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchForms() {
      const { data, error } = await supabase
        .from("company_forms")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching forms:", error);
        return;
      }

      setForms(data || []);
      setLoading(false);
    }

    fetchForms();
  }, []);

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-4 sm:py-8 px-6 sm:px-1">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>

          <Card className="overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50/50">
                    <TableHead>Datum</TableHead>
                    <TableHead>Naam</TableHead>
                    <TableHead>Bedrijf</TableHead>
                    <TableHead>Type Personeel</TableHead>
                    <TableHead>KVK</TableHead>
                    <TableHead>Aantal</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(5)].map((_, index) => (
                    <TableRowSkeleton key={index} />
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-4 sm:py-8 px-6 sm:px-1">
      <div className="space-y-4 sm:space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="space-y-1">
            <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
              Bedrijfsaanvragen
            </h1>
            <p className="text-sm text-gray-500">
              Overzicht van alle ingediende bedrijfsaanvragen
            </p>
          </div>
          <Badge
            variant="secondary"
            className="self-start sm:self-center px-3 py-1.5"
          >
            {forms.length} aanvragen
          </Badge>
        </div>

        {/* Table Card */}
        <Card className="overflow-hidden border-gray-200">
          <div className="overflow-x-auto -mx-2 sm:mx-0">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50/50">
                  <TableHead className="font-semibold whitespace-nowrap">
                    Datum
                  </TableHead>
                  <TableHead className="font-semibold min-w-[120px]">
                    Naam
                  </TableHead>
                  <TableHead className="font-semibold min-w-[150px]">
                    Bedrijf
                  </TableHead>
                  <TableHead className="font-semibold min-w-[140px]">
                    Type
                  </TableHead>
                  <TableHead className="font-semibold whitespace-nowrap">
                    KVK
                  </TableHead>
                  <TableHead className="font-semibold whitespace-nowrap">
                    Aantal
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forms.map((form) => (
                  <TableRow
                    key={form.id}
                    className="hover:bg-gray-50/50 transition-colors cursor-pointer"
                  >
                    <TableCell className="whitespace-nowrap">
                      <div className="flex items-center gap-2 text-gray-600">
                        <Calendar className="hidden sm:block h-4 w-4" />
                        <span className="text-sm sm:text-base">
                          {format(new Date(form.created_at), "d MMM yyyy", {
                            locale: nl,
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm sm:text-base line-clamp-1">
                        {form.name}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="hidden sm:block h-4 w-4 text-gray-500" />
                        <span className="text-sm sm:text-base line-clamp-1">
                          {form.company_name}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm sm:text-base line-clamp-1">
                        {form.personnel_type}
                      </span>
                    </TableCell>
                    <TableCell className="font-mono text-sm sm:text-base">
                      {form.kvk_number}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="hidden sm:block h-4 w-4 text-gray-500" />
                        <span className="text-sm sm:text-base">
                          {form.personnel_count}
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </Card>
      </div>
    </div>
  );
}
