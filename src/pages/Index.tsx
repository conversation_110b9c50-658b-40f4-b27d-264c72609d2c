import { useState } from "react";
import { Loader2 } from "lucide-react";

import { ResizablePanelGroup, ResizablePanel } from "@/components/ui/resizable";
import { VakmanDashboard } from "@/components/VakmanDashboard";
import { KlusaanvragerDashboard } from "@/components/KlusaanvragerDashboard";
import { ProfileSection } from "@/components/ProfileSection";
import Balance from "./Balance";
import Reviews from "./Reviews";
import { MyResponses } from "@/components/MyResponses";
import { Toaster } from "@/components/ui/toaster";
import AdminDashboard from "./AdminDashboard";
import { useAuth } from "@/components/auth/hooks/useAuth";

const Index = () => {
  const [currentView, setCurrentView] = useState<string>("dashboard");
  const { userProfile } = useAuth();

  if (!userProfile) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const renderContent = () => {
    if (userProfile.user_type === "admin") {
      return <AdminDashboard />;
    }

    if (userProfile.user_type === "klusaanvrager") {
      switch (currentView) {
        case "dashboard":
          return <KlusaanvragerDashboard setCurrentView={setCurrentView} />;
        case "profile":
          return <ProfileSection />;
        default:
          return <KlusaanvragerDashboard setCurrentView={setCurrentView} />;
      }
    } else {
      switch (currentView) {
        case "dashboard":
          return <VakmanDashboard />;
        case "balance":
          return <Balance />;
        case "my-responses":
          return <MyResponses />;
        case "profile":
          return <ProfileSection />;
        case "reviews":
          return <Reviews />;
        default:
          return <VakmanDashboard />;
      }
    }
  };

  return (
    <>
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        <ResizablePanel defaultSize={100}>
          <main className="h-full overflow-auto w-full">
            <div className="max-w-7xl mx-auto">{renderContent()}</div>
          </main>
        </ResizablePanel>
      </ResizablePanelGroup>
      <Toaster />
    </>
  );
};

export default Index;
